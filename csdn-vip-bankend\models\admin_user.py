from .base import BaseModel, fields


class AdminUser(BaseModel):
    """后台管理用户表"""
    username = fields.Char<PERSON>ield(max_length=64, unique=True, null=False, description="用户名")
    nickname = fields.Char<PERSON>ield(max_length=64, null=True, description="昵称")
    hashed_password = fields.CharField(max_length=128, null=False, description="密码")
    phone = fields.CharField(max_length=11, null=True, description="手机")
    avatar = fields.CharField(max_length=256, null=True, description="头像")
    email = fields.CharField(max_length=256, null=True, description="邮箱")
    is_super = fields.BooleanField(default=False, description="超级用户")
    is_active = fields.BooleanField(default=False, description="是否激活")
    last_login_time = fields.DatetimeField(null=True, description="最后登录时间")

    class Meta:
        table = "admin_user"

    class PydanticMeta:
        exclude = ['is_deleted', 'is_super']


