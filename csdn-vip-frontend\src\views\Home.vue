<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="nav-left">
          <h2 class="nav-logo">CSDN提取系统</h2>
        </div>
        <div class="nav-right">
          <el-button text @click="goToAdmin" class="nav-link">
            <el-icon><UserFilled /></el-icon>
            管理员登录
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="hero-section">
      <h1 class="title">CSDN文章提取系统</h1>
      <p class="subtitle">快速、稳定、高效的CSDN文章内容提取服务</p>
      
      <div class="token-input-section">
        <el-input
          v-model="inputToken"
          placeholder="请输入您的访问令牌"
          size="large"
          class="token-input"
          @keyup.enter="validateAndStart"
        >
          <template #append>
            <el-button 
              type="primary" 
              @click="validateAndStart"
              :loading="isValidating"
            >
              验证并开始
            </el-button>
          </template>
        </el-input>
      </div>

      <div v-if="tokenValidated" class="token-info">
        <el-card class="info-card">
          <div class="token-status">
            <el-tag type="success" size="large">
              <el-icon><Check /></el-icon>
              令牌验证成功
            </el-tag>
          </div>
          
          <div class="token-details">
            <div class="detail-item" v-if="extractionStore.remainingCount !== null">
              <span class="label">剩余次数：</span>
              <span class="value">{{ extractionStore.remainingCount }}</span>
            </div>
            <div class="detail-item" v-if="extractionStore.remainingTime">
              <span class="label">剩余时间：</span>
              <span class="value">{{ formatTime(extractionStore.remainingTime) }}</span>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="primary" size="large" @click="goToExtract">
              <el-icon><DocumentAdd /></el-icon>
              开始提取文章
            </el-button>
            <el-button type="info" size="large" @click="goToHistory">
              <el-icon><Clock /></el-icon>
              查看提取历史
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <div class="features-section">
      <h2>系统特色</h2>
      <div class="features-grid">
        <div class="feature-card">
          <el-icon class="feature-icon" color="#409EFF"><Lightning /></el-icon>
          <h3>快速提取</h3>
          <p>高效的提取算法，秒级获取文章内容</p>
        </div>
        <div class="feature-card">
          <el-icon class="feature-icon" color="#67C23A"><Lock /></el-icon>
          <h3>安全可靠</h3>
          <p>完善的权限控制和安全防护机制</p>
        </div>
        <div class="feature-card">
          <el-icon class="feature-icon" color="#E6A23C"><DataAnalysis /></el-icon>
          <h3>智能去重</h3>
          <p>自动识别重复文章，避免重复提取</p>
        </div>
        <div class="feature-card">
          <el-icon class="feature-icon" color="#F56C6C"><TrendCharts /></el-icon>
          <h3>使用统计</h3>
          <p>详细的使用记录和统计分析</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Check, DocumentAdd, Clock, Lightning, Lock, DataAnalysis, TrendCharts, UserFilled } from '@element-plus/icons-vue'
import { useExtractionStore } from '../stores/extraction'
import extractionApi from '../api/extraction'

const router = useRouter()
const route = useRoute()
const extractionStore = useExtractionStore()

const inputToken = ref('')
const isValidating = ref(false)
const tokenValidated = ref(false)

// 验证令牌并开始
const validateAndStart = async () => {
  if (!inputToken.value.trim()) {
    ElMessage.warning('请输入访问令牌')
    return
  }

  isValidating.value = true
  
  try {
    const response = await extractionApi.validateToken({
      token: inputToken.value.trim()
    })

    if (response.valid) {
      extractionStore.setToken(inputToken.value.trim())
      extractionStore.setTokenInfo(response)
      tokenValidated.value = true
      ElMessage.success('令牌验证成功！')
    } else {
      ElMessage.error(response.error_message || '令牌验证失败')
      tokenValidated.value = false
    }
  } catch (error) {
    console.error('Token validation failed:', error)
    ElMessage.error('令牌验证失败，请检查网络连接')
    tokenValidated.value = false
  } finally {
    isValidating.value = false
  }
}

// 跳转到提取页面
const goToExtract = () => {
  router.push('/extract')
}

// 跳转到历史页面
const goToHistory = () => {
  router.push('/history')
}

// 跳转到管理员登录
const goToAdmin = () => {
  router.push('/login')
}

// 格式化时间
const formatTime = (timeStr: string) => {
  // 简单的时间格式化，可以根据需要优化
  try {
    const match = timeStr.match(/(\d+):(\d+):(\d+)/)
    if (match) {
      const [, hours, minutes, seconds] = match
      return `${hours}小时${minutes}分钟${seconds}秒`
    }
    return timeStr
  } catch {
    return timeStr
  }
}

// 页面初始化
onMounted(() => {
  // 初始化token
  extractionStore.initToken()
  
  // 检查URL参数中的token
  const urlToken = route.query.token as string
  if (urlToken) {
    inputToken.value = urlToken
    validateAndStart()
  } else if (extractionStore.currentToken) {
    // 如果已有保存的token，自动验证
    inputToken.value = extractionStore.currentToken
    validateAndStart()
  }
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 顶部导航样式 */
.top-nav {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 14px;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: white !important;
}

.hero-section {
  text-align: center;
  margin-bottom: 4rem;
  padding: 4rem 2rem 2rem;
  color: white;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.token-input-section {
  max-width: 600px;
  margin: 0 auto 2rem;
}

.token-input {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.token-info {
  max-width: 500px;
  margin: 0 auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
}

.token-status {
  margin-bottom: 1.5rem;
}

.token-details {
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  color: #606266;
}

.label {
  font-weight: 500;
}

.value {
  font-weight: bold;
  color: #409EFF;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.features-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 4rem;
  text-align: center;
  color: white;
}

.features-section h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: white;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .top-nav {
    padding: 1rem;
  }
  
  .nav-logo {
    font-size: 1rem;
  }
  
  .hero-section {
    padding: 2rem 1rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
  
  .token-input-section {
    max-width: 100%;
    margin: 0 auto 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .features-section {
    padding: 0 1rem 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .features-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
}
</style>
