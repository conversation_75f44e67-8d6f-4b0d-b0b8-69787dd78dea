# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/6/9 09:49
# @File     : filetools.py
# @Project  : template
import hashlib

import os
import logging
import shutil
import zipfile


class FileTools:
    """
    文件操作类
    """
    def __init__(self, src: str, dst: str) -> None:
        self.src = src  # 源文件
        self.dst = dst  # 目标文件

    def copy_file(self):
        """
        复制文件
        """
        try:
            shutil.copy(self.src, self.dst)
        except Exception as err:
            logging.error(err)

    def copy_folder(self):
        """
        复制文件夹
        """
        try:
            shutil.copytree(self.src, self.dst)
        except Exception as err:
            logging.error(err)

    def move_file(self):
        """
        移动文件
        """
        try:
            shutil.move(self.src, self.dst)
        except Exception as err:
            logging.error(err)

    def move_folder(self):
        """
        移动文件夹
        """
        try:
            shutil.move(self.src, self.dst)
        except Exception as err:
            logging.error(err)

    def delete_file(self):
        """
        删除文件
        """
        try:
            os.remove(self.src)
        except Exception as err:
            logging.error(err)

    def delete_folder(self):
        """
        删除文件夹
        """
        try:
            shutil.rmtree(self.src)
        except Exception as err:
            logging.error(err)

    def rename_file(self, new_name: str):
        """
        重命名文件
        """
        try:
            os.rename(self.src, new_name)
        except Exception as err:
            logging.error(err)

    def rename_folder(self, new_name: str):
        """
        重命名文件夹
        """
        try:
            os.rename(self.src, new_name)
        except Exception as err:
            logging.error(err)

    def zip_file(self, zip_name: str):
        """
        压缩文件
        """
        try:
            zip = zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED)
            zip.write(self.src)
            zip.close()
        except Exception as err:
            logging.error(err)

    def unzip_file(self, zip_name: str):
        """
        解压文件
        """
        try:
            zip = zipfile.ZipFile(zip_name, 'r', zipfile.ZIP_DEFLATED)
            zip.extractall(self.dst)
            zip.close()
        except Exception as err:
            logging.error(err)

    def zip_folder(self, zip_name: str):
        """
        压缩文件夹
        """
        try:
            zip = zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED)
            for root, dirs, files in os.walk(self.src):
                for file in files:
                    zip.write(os.path.join(root, file))
            zip.close()
        except Exception as err:
            logging.error(err)

    def unzip_folder(self, zip_name: str):
        """
        解压文件夹
        """
        try:
            zip = zipfile.ZipFile(zip_name, 'r', zipfile.ZIP_DEFLATED)
            zip.extractall(self.dst)
            zip.close()
        except Exception as err:
            logging.error(err)

    def get_file_size(self):
        """
        获取文件大小
        """
        try:
            size = os.path.getsize(self.src)
            return self.format_size(size)
        except Exception as err:
            logging.error(err)

    def get_folder_size(self):
        """
        获取文件夹大小
        """
        sumsize = 0
        try:
            filename = os.walk(self.src)
            for root, dirs, files in filename:
                for fle in files:
                    size = os.path.getsize(self.src + fle)
                    sumsize += size
            return self.format_size(sumsize)
        except Exception as err:
            logging.error(err)

    def format_size(self, bytes):
        """
        格式化文件大小
        """
        try:
            bytes = float(bytes)
            kb = bytes / 1024
        except:
            logging.error("传入的字节格式不对")
            return "Error"
        if kb >= 1024:
            M = kb / 1024
            if M >= 1024:
                G = M / 1024
                return "%fG" % (G)
            else:
                return "%fM" % (M)
        else:
            return "%fkb" % (kb)

    # 获取文件夹中文件所有后缀
    def get_suffix(self):
        """
        获取文件夹中文件所有后缀
        """
        try:
            suffix = set()
            file_list = os.listdir(self.src)
            for file in file_list:
                suffix.add(os.path.splitext(file)[1])
            return suffix
        except Exception as err:
            logging.error(err)

    def get_file_list(self):
        """
        获取文件列表
        """
        try:
            file_list = os.listdir(self.src)
            return file_list
        except Exception as err:
            logging.error(err)

    def get_folder_list(self):
        """
        获取文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            return folder_list
        except Exception as err:
            logging.error(err)

    def get_file_list_by_suffix(self, suffix: str):
        """
        获取指定后缀文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_suffix = []
            for file in file_list:
                if file.endswith(suffix):
                    file_list_by_suffix.append(file)
            return file_list_by_suffix
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_suffix(self, suffix: str):
        """
        获取指定后缀文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_suffix = []
            for folder in folder_list:
                if folder.endswith(suffix):
                    folder_list_by_suffix.append(folder)
            return folder_list_by_suffix
        except Exception as err:
            logging.error(err)

    def get_file_list_by_prefix(self, prefix: str):
        """
        获取指定前缀文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_prefix = []
            for file in file_list:
                if file.startswith(prefix):
                    file_list_by_prefix.append(file)
            return file_list_by_prefix
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_prefix(self, prefix: str):
        """
        获取指定前缀文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_prefix = []
            for folder in folder_list:
                if folder.startswith(prefix):
                    folder_list_by_prefix.append(folder)
            return folder_list_by_prefix
        except Exception as err:
            logging.error(err)

    def get_file_list_by_suffix_and_prefix(self, suffix: str, prefix: str):
        """
        获取指定后缀和前缀文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_suffix_and_prefix = []
            for file in file_list:
                if file.startswith(prefix) and file.endswith(suffix):
                    file_list_by_suffix_and_prefix.append(file)
            return file_list_by_suffix_and_prefix
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_suffix_and_prefix(self, suffix: str, prefix: str):
        """
        获取指定后缀和前缀文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_suffix_and_prefix = []
            for folder in folder_list:
                if folder.startswith(prefix) and folder.endswith(suffix):
                    folder_list_by_suffix_and_prefix.append(folder)
            return folder_list_by_suffix_and_prefix
        except Exception as err:
            logging.error(err)

    def get_file_list_by_suffix_and_prefix_and_contains(self, suffix: str, prefix: str, contains: str):
        """
        获取指定后缀和前缀和包含字符串文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_suffix_and_prefix_and_contains = []
            for file in file_list:
                if file.startswith(prefix) and file.endswith(suffix) and contains in file:
                    file_list_by_suffix_and_prefix_and_contains.append(file)
            return file_list_by_suffix_and_prefix_and_contains
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_suffix_and_prefix_and_contains(self, suffix: str, prefix: str, contains: str):
        """
        获取指定后缀和前缀和包含字符串文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_suffix_and_prefix_and_contains = []
            for folder in folder_list:
                if folder.startswith(prefix) and folder.endswith(suffix) and contains in folder:
                    folder_list_by_suffix_and_prefix_and_contains.append(folder)
            return folder_list_by_suffix_and_prefix_and_contains
        except Exception as err:
            logging.error(err)

    def get_file_list_by_contains(self, contains: str):
        """
        获取包含字符串文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_contains = []
            for file in file_list:
                if contains in file:
                    file_list_by_contains.append(file)
            return file_list_by_contains
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_contains(self, contains: str):
        """
        获取包含字符串文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_contains = []
            for folder in folder_list:
                if contains in folder:
                    folder_list_by_contains.append(folder)
            return folder_list_by_contains
        except Exception as err:
            logging.error(err)

    def get_file_list_by_suffix_and_contains(self, suffix: str, contains: str):
        """
        获取指定后缀和包含字符串文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_suffix_and_contains = []
            for file in file_list:
                if file.endswith(suffix) and contains in file:
                    file_list_by_suffix_and_contains.append(file)
            return file_list_by_suffix_and_contains
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_suffix_and_contains(self, suffix: str, contains: str):
        """
        获取指定后缀和包含字符串文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_suffix_and_contains = []
            for folder in folder_list:
                if folder.endswith(suffix) and contains in folder:
                    folder_list_by_suffix_and_contains.append(folder)
            return folder_list_by_suffix_and_contains
        except Exception as err:
            logging.error(err)

    def get_file_list_by_prefix_and_contains(self, prefix: str, contains: str):
        """
        获取指定前缀和包含字符串文件列表
        """
        try:
            file_list = os.listdir(self.src)
            file_list_by_prefix_and_contains = []
            for file in file_list:
                if file.startswith(prefix) and contains in file:
                    file_list_by_prefix_and_contains.append(file)
            return file_list_by_prefix_and_contains
        except Exception as err:
            logging.error(err)

    def get_folder_list_by_prefix_and_contains(self, prefix: str, contains: str):
        """
        获取指定前缀和包含字符串文件夹列表
        """
        try:
            folder_list = os.listdir(self.src)
            folder_list_by_prefix_and_contains = []
            for folder in folder_list:
                if folder.startswith(prefix) and contains in folder:
                    folder_list_by_prefix_and_contains.append(folder)
            return folder_list_by_prefix_and_contains
        except Exception as err:
            logging.error(err)


class File:
    """
    文件类
    """
    def __init__(self, src: str):
        self.src = src

    def get_file_name(self):
        """
        获取文件名
        """
        try:
            file_name = os.path.basename(self.src)
            return file_name
        except Exception as err:
            logging.error(err)

    def get_file_path(self):
        """
        获取文件路径
        """
        try:
            file_path = os.path.dirname(self.src)
            return file_path
        except Exception as err:
            logging.error(err)

    def get_file_size(self):
        """
        获取文件大小
        """
        try:
            file_size = os.path.getsize(self.src)
            return file_size
        except Exception as err:
            logging.error(err)

    def get_file_create_time(self):
        """
        获取文件创建时间
        """
        try:
            file_create_time = os.path.getctime(self.src)
            return file_create_time
        except Exception as err:
            logging.error(err)

    def get_file_modify_time(self):
        """
        获取文件修改时间
        """
        try:
            file_modify_time = os.path.getmtime(self.src)
            return file_modify_time
        except Exception as err:
            logging.error(err)

    def get_file_access_time(self):
        """
        获取文件访问时间
        """
        try:
            file_access_time = os.path.getatime(self.src)
            return file_access_time
        except Exception as err:
            logging.error(err)

    def get_file_md5(self):
        """
        获取文件MD5
        """
        try:
            file_md5 = hashlib.md5(open(self.src, 'rb').read()).hexdigest()
            return file_md5
        except Exception as err:
            logging.error(err)

    def get_file_sha1(self):
        """
        获取文件SHA1
        """
        try:
            file_sha1 = hashlib.sha1(open(self.src, 'rb').read()).hexdigest()
            return file_sha1
        except Exception as err:
            logging.error(err)

    def get_file_sha256(self):

        """
        获取文件SHA256
        """
        try:
            file_sha256 = hashlib.sha256(open(self.src, 'rb').read()).hexdigest()
            return file_sha256
        except Exception as err:
            logging.error(err)

    def get_file_sha512(self):
        """
        获取文件SHA512
        """
        try:
            file_sha512 = hashlib.sha512(open(self.src, 'rb').read()).hexdigest()
            return file_sha512
        except Exception as err:
            logging.error(err)


class Folder:
    """
    文件夹类
    """
    def __init__(self, src: str):
        self.src = src

    def get_folder_name(self):
        """
        获取文件夹名
        """
        try:
            folder_name = os.path.basename(self.src)
            return folder_name
        except Exception as err:
            logging.error(err)

    def get_folder_path(self):
        """
        获取文件夹路径
        """
        try:
            folder_path = os.path.dirname(self.src)
            return folder_path
        except Exception as err:
            logging.error(err)

    def get_folder_size(self):
        """
        获取文件夹大小
        """
        try:
            folder_size = os.path.getsize(self.src)
            return folder_size
        except Exception as err:
            logging.error(err)

    def get_folder_create_time(self):
        """
        获取文件夹创建时间
        """
        try:
            folder_create_time = os.path.getctime(self.src)
            return folder_create_time
        except Exception as err:
            logging.error(err)

    def get_folder_modify_time(self):
        """
        获取文件夹修改时间
        """
        try:
            folder_modify_time = os.path.getmtime(self.src)
            return folder_modify_time
        except Exception as err:
            logging.error(err)

    def get_folder_access_time(self):
        """
        获取文件夹访问时间
        """
        try:
            folder_access_time = os.path.getatime(self.src)
            return folder_access_time
        except Exception as err:
            logging.error(err)

    def get_folder_md5(self):
        """
        获取文件夹MD5
        """
        try:
            folder_md5 = hashlib.md5(open(self.src, 'rb').read()).hexdigest()
            return folder_md5
        except Exception as err:
            logging.error(err)

    def get_folder_sha1(self):
        """
        获取文件夹SHA1
        """
        try:
            folder_sha1 = hashlib.sha1(open(self.src, 'rb').read()).hexdigest()
            return folder_sha1
        except Exception as err:
            logging.error(err)

    def get_folder_sha256(self):

        """
        获取文件夹SHA256
        """
        try:
            folder_sha256 = hashlib.sha256(open(self.src, 'rb').read()).hexdigest()
            return folder_sha256
        except Exception as err:
            logging.error(err)

    def get_folder_sha512(self):

        """
        获取文件夹SHA512
        """
        try:
            folder_sha512 = hashlib.sha512(open(self.src, 'rb').read()).hexdigest()
            return folder_sha512
        except Exception as err:
            logging.error(err)
