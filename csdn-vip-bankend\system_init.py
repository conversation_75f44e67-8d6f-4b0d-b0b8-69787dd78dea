# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/5/20 17:13
# @File     : system_init.py
# @Project  : template


""" 系统初始化脚本:

    第一次运行程序之后，运行此初始化脚本。
    新建超级管理员角色(super_role)，默认用户角色(default_role)，超级管理员用户(admin)。

    1. 新建超级管理员角色(super_role: role_id=1

    2. 新建默认用户角色(default_role: role_id=2)

    3. 新建超级管理员用户(admin: id=1)
        username: admin
        password: Admin123

    4. 系统默认资源

    5. 系统菜单及行为操作

    bcrypt: 4.1.2  版本中去掉了__about__模块，所以用到后可能会报错=> module 'bcrypt' has no attribute '__about__'


"""
from pprint import pprint

import casbin
import casbin_tortoise_adapter
from typing import Any
from passlib.context import CryptContext
from tortoise import Tortoise, run_async
from models import AdminRole, AdminUser, AdminAction, AdminObject
from config import settings
from utils.base import Singleton


class TortoiseCasbin(metaclass=Singleton):
    def __init__(self, model: str) -> None:
        adapter = casbin_tortoise_adapter.TortoiseAdapter()
        self.enforce = casbin.Enforcer(str(model), adapter)

    async def has_permission(self, user: str, obj: str, act: str) -> bool:
        """
        判断是否拥有权限
        """
        return self.enforce.enforce(user, obj, act)

    async def add_permission_for_role(self, role: str, obj: str, act: str):
        """
        添加角色权限
        """
        return await self.enforce.add_policy(role, obj, act)

    async def get_permissions_for_user_or_role(self, user_or_role: str):
        """
        gets permissions for a user or role.
        """
        return await self.enforce.get_filtered_policy(0, user_or_role)

    async def remove_permission(self, sub: str, obj: str, act: str):
        return await self.enforce.remove_policy(sub, obj, act)

    def __getattr__(self, attr: str) -> Any:
        return getattr(self.enforce, attr)


async def db_init():
    await Tortoise.init(
        config={
            "connections": {
                "postgresql": f"postgres://"
                              f"{settings.POSTGRESQL.USER}:"
                              f"{settings.POSTGRESQL.PASSWORD}@"
                              f"{settings.POSTGRESQL.HOST}:"
                              f"{settings.POSTGRESQL.PORT}/"
                              f"{settings.POSTGRESQL.DATABASE}",
            },
            "apps": {
                # 下面的 template 需要替换成自己的应用名，应用名看 main.py 中的 TORTOISE_ORM 里的 apps 里面的键
                "template": {
                    "models": [
                        "models",
                        "casbin_tortoise_adapter",
                    ],
                    "default_connection": "postgresql",
                }
            },
            'use_tz': False,
            'timezone': 'Asia/Shanghai'
        }
    )


async def init_admin_and_role():
    # 查找 admin 用户是否存在
    admin = await AdminUser.get_or_none(username='admin')
    if not admin:  # 如果不存在，则创建
        admin_orm = await AdminUser.create(id=1, create_by='system', remark='系统默认创建超级管理员用户',
                                           username='admin',
                                           nickname='admin',
                                           hashed_password=CryptContext(schemes=["bcrypt"], deprecated="auto").hash(
                                               'Admin123'), phone=12233335555,
                                           avatar='/dog.svg', email='<EMAIL>', is_super=True,
                                           is_active=True, is_deleted=False)
        print('创建超级管理员用户成功')
    else:  # 如果存在，则提示
        print('超级管理员用户已存在，请在后台程序初始化时运行此脚本')

    # 创建超级管理员角色
    super_role = await AdminRole.get_or_none(role='super_role')
    if not super_role:  # 如果不存在，则创建
        super_role_orm = await AdminRole.create(id=1, create_by='system', remark='系统默认创建超级管理员角色',
                                                name='超级管理员', role='super_role',
                                                parent_role=0)
        print('创建超级管理员角色成功')
    else:  # 如果存在，则提示
        print('超级管理员角色已存在，请在后台程序初始化时运行此脚本')

    # 创建默认角色组
    default_role = await AdminRole.get_or_none(role='default_role')
    if not default_role:  # 如果不存在，则创建
        default_role_orm = await AdminRole.create(id=2, create_by='system', remark='系统默认创建默认用户角色',
                                                  name='默认用户', role='default_role',
                                                  parent_role=0)
        print('创建默认用户角色成功')
    else:  # 如果存在，则提示
        print('默认用户角色已存在，请在后台程序初始化时运行此脚本')

    # await AdminRole.filter(id=1).update(role_admin=1)
    # await AdminRole.filter(id=2).update(role_admin=1)

    # 把超级管理员和超级管理员角色关联起来
    e = TortoiseCasbin(settings.CASBIN.CASBIN_MODEL_PATH)
    res = await e.add_role_for_user("admin", "super_role")
    print(res, '默认超级管理员与超级管理员角色关联成功')


# 默认创建一条数据
# async def create_project_source_default():
#     source_default = await ProjectDefaultResource.get_or_none(id=1)
#     if not source_default:  # 如果不存在，则创建
#         admin_orm = await ProjectDefaultResource.create(id=1, create_by='system', update_by='system',
#                                                         remark='系统默认创建项目资源分配', is_deleted=False)
#         print('创建项目资源分配成功')
#     else:  # 如果存在，则提示
#         print('项目资源已存在，请在后台程序初始化时运行此脚本')


async def create_objs_acts_default():
    """
    obj: 对象
    objs_dict_list: 对象字典列表
    [{"name": "超级管理员","obj": "/superAdmin","type": "menu"},{"name": "菜单管理","obj": "/superAdmin/menumanagement","type": "menu"},{"name": "角色管理","obj": "/superAdmin/rolesmanagement","type": "menu"},{"name": "api管理","obj": "/superAdmin/apimanagement","type": "menu"},{"name": "用户管理","obj": "/superAdmin/usermanagement","type": "menu"},{"name": "项目管理","obj": "/project/manage","type": "menu"},{"name": "工作流画布","obj": "/ground/canvas","type": "menu"},{"name": "工作流运行历史","obj": "/ground/detail","type": "menu"},{"name": "项目数据集","obj": "/project/DataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "menu"},{"name": "算子管理","obj": "/project/operator","type": "menu"},{"name": "算子管理","obj": "/operator","type": "menu"},{"name": "算子管理","obj": "/operator/main","type": "menu"},{"name": "流程管理","obj": "/oa/flow_manage","type": "menu"},{"name": "算子管理主页","obj": "/operator/main","type": "menu"},{"name": "算子管理编辑界面","obj": "/operator/zip","type": "menu"},{"name": "血缘图","obj": "/bloodMap","type": "menu"},{"name": "审批","obj": "/oa/apply","type": "menu"},{"name": "审批","obj": "/oa","type": "menu"},{"name": "项目-算子编辑","obj": "/project/operator","type": "menu"},{"name": "血缘图","obj": "/bloodMap/main","type": "menu"},{"name": "运行历史","obj": "/ground/canvasTask","type": "menu"},{"name": "看板仓库","obj": "/project/BiSelect","type": "api"},{"name": "元数据管理","obj": "/data/metadata","type": "menu"},{"name": "看板仓库","obj": "/project/BiSelect","type": "menu"},{"name": "历史版本","obj": "/ground/versions","type": "menu"},{"name": "分配资源-项目","obj": "/systemManagement/allocateResourcesProject","type": "menu"},{"name": "/metadata","obj": "/metadata","type": "api"},{"name": "/sqllab","obj": "/sqllab","type": "api"},{"name": "/data","obj": "/data","type": "api"},{"name": "/systemManagement/projectLevel","obj": "/systemManagement/projectLevel","type": "api"},{"name": "/operator","obj": "/operator","type": "api"},{"name": "/systemManagement/projectInternal","obj": "/systemManagement/projectInternal","type": "api"},{"name": "SQL编辑器","obj": "/sqllab","type": "api"},{"name": "/systemManagement/allocateResourcesRoles","obj": "/systemManagement/allocateResourcesRoles","type": "api"},{"name": "SQL编辑器","obj": "/data/sqllab","type": "menu"},{"name": "数据集管理主菜单","obj": "/data","type": "menu"},{"name": "项目管理","obj": "/project","type": "menu"},{"name": "项目预定计划","obj": "/project/main/schedule","type": "menu"},{"name": "数据集管理","obj": "/data/manage","type": "menu"},{"name": "审批中心","obj": "/oa/center","type": "menu"},{"name": "/systemManagement/allocateResourcesUser","obj": "/systemManagement/allocateResourcesUser","type": "api"},{"name": "项目任务","obj": "/project/main/tasks","type": "menu"},{"name": "发起审批","obj": "/oa/apply","type": "menu"},{"name": "/systemManagement/allocateResourcesProject","obj": "/systemManagement/allocateResourcesProject","type": "api"},{"name": "主要项目","obj": "/project/main","type": "menu"},{"name": "模型管理","obj": "/model","type": "menu"},{"name": "分配资源-角色","obj": "/systemManagement/allocateResourcesRoles","type": "menu"},{"name": "血缘图","obj": "/bloodMap/graph","type": "api"},{"name": "/operator/main","obj": "/operator/main","type": "api"},{"name": "项目模型仓库","obj": "/project/main/model","type": "menu"},{"name": "项目资源","obj": "/systemManagement","type": "menu"},{"name": "项目级","obj": "/systemManagement/projectLevel","type": "menu"},{"name": "项目内","obj": "/systemManagement/projectInternal","type": "menu"},{"name": "分配资源-用户","obj": "/systemManagement/allocateResourcesUser","type": "menu"},{"name": "血缘图","obj": "/data/bloodMap","type": "menu"},{"name": "数据建模","obj": "/ground","type": "menu"},{"name": "项目管理-删除","obj": "/project/manage","type": "api"},{"name": "数据管理","obj": "/data","type": "menu"},{"name": "数据集管理-删除","obj": "/project/main","type": "api"},{"name": "血缘图","obj": "/bloodMap/main","type": "api"},{"name": "流程管理","obj": "/oa/flow_manage","type": "api"}]

    act: 操作
    acts_dict_list: 操作字典列表
    [{"name": "DELETE","act": "DELETE"},{"name": "SHOW","act": "SHOW"},{"name": "PUT","act": "PUT"},{"name": "POST","act": "POST"},{"name": "GET","act": "GET"}]
    """
    create_by = "system"
    remark = '系统默认创建'
    objs_dict_list = [{"name": "超级管理员","obj": "/superAdmin","type": "menu"},{"name": "菜单管理","obj": "/superAdmin/menumanagement","type": "menu"},{"name": "角色管理","obj": "/superAdmin/rolesmanagement","type": "menu"},{"name": "api管理","obj": "/superAdmin/apimanagement","type": "menu"},{"name": "用户管理","obj": "/superAdmin/usermanagement","type": "menu"},{"name": "项目管理","obj": "/project/manage","type": "menu"},{"name": "工作流画布","obj": "/ground/canvas","type": "menu"},{"name": "工作流运行历史","obj": "/ground/detail","type": "menu"},{"name": "项目数据集","obj": "/project/DataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "api"},{"name": "项目数据集","obj": "/project/dataManage","type": "menu"},{"name": "算子管理","obj": "/project/operator","type": "menu"},{"name": "算子管理","obj": "/operator","type": "menu"},{"name": "算子管理","obj": "/operator/main","type": "menu"},{"name": "流程管理","obj": "/oa/flow_manage","type": "menu"},{"name": "算子管理主页","obj": "/operator/main","type": "menu"},{"name": "算子管理编辑界面","obj": "/operator/zip","type": "menu"},{"name": "血缘图","obj": "/bloodMap","type": "menu"},{"name": "审批","obj": "/oa/apply","type": "menu"},{"name": "审批","obj": "/oa","type": "menu"},{"name": "项目-算子编辑","obj": "/project/operator","type": "menu"},{"name": "血缘图","obj": "/bloodMap/main","type": "menu"},{"name": "运行历史","obj": "/ground/canvasTask","type": "menu"},{"name": "看板仓库","obj": "/project/BiSelect","type": "api"},{"name": "元数据管理","obj": "/data/metadata","type": "menu"},{"name": "看板仓库","obj": "/project/BiSelect","type": "menu"},{"name": "历史版本","obj": "/ground/versions","type": "menu"},{"name": "分配资源-项目","obj": "/systemManagement/allocateResourcesProject","type": "menu"},{"name": "/metadata","obj": "/metadata","type": "api"},{"name": "/sqllab","obj": "/sqllab","type": "api"},{"name": "/data","obj": "/data","type": "api"},{"name": "/systemManagement/projectLevel","obj": "/systemManagement/projectLevel","type": "api"},{"name": "/operator","obj": "/operator","type": "api"},{"name": "/systemManagement/projectInternal","obj": "/systemManagement/projectInternal","type": "api"},{"name": "SQL编辑器","obj": "/sqllab","type": "api"},{"name": "/systemManagement/allocateResourcesRoles","obj": "/systemManagement/allocateResourcesRoles","type": "api"},{"name": "SQL编辑器","obj": "/data/sqllab","type": "menu"},{"name": "数据集管理主菜单","obj": "/data","type": "menu"},{"name": "项目管理","obj": "/project","type": "menu"},{"name": "项目预定计划","obj": "/project/main/schedule","type": "menu"},{"name": "数据集管理","obj": "/data/manage","type": "menu"},{"name": "审批中心","obj": "/oa/center","type": "menu"},{"name": "/systemManagement/allocateResourcesUser","obj": "/systemManagement/allocateResourcesUser","type": "api"},{"name": "项目任务","obj": "/project/main/tasks","type": "menu"},{"name": "发起审批","obj": "/oa/apply","type": "menu"},{"name": "/systemManagement/allocateResourcesProject","obj": "/systemManagement/allocateResourcesProject","type": "api"},{"name": "主要项目","obj": "/project/main","type": "menu"},{"name": "模型管理","obj": "/model","type": "menu"},{"name": "分配资源-角色","obj": "/systemManagement/allocateResourcesRoles","type": "menu"},{"name": "血缘图","obj": "/bloodMap/graph","type": "api"},{"name": "/operator/main","obj": "/operator/main","type": "api"},{"name": "项目模型仓库","obj": "/project/main/model","type": "menu"},{"name": "项目资源","obj": "/systemManagement","type": "menu"},{"name": "项目级","obj": "/systemManagement/projectLevel","type": "menu"},{"name": "项目内","obj": "/systemManagement/projectInternal","type": "menu"},{"name": "分配资源-用户","obj": "/systemManagement/allocateResourcesUser","type": "menu"},{"name": "血缘图","obj": "/data/bloodMap","type": "menu"},{"name": "数据建模","obj": "/ground","type": "menu"},{"name": "项目管理-删除","obj": "/project/manage","type": "api"},{"name": "数据管理","obj": "/data","type": "menu"},{"name": "数据集管理-删除","obj": "/project/main","type": "api"},{"name": "血缘图","obj": "/bloodMap/main","type": "api"},{"name": "流程管理","obj": "/oa/flow_manage","type": "api"}]
    acts_dict_list = [{"name": "DELETE","act": "DELETE"},{"name": "SHOW","act": "SHOW"},{"name": "PUT","act": "PUT"},{"name": "POST","act": "POST"},{"name": "GET","act": "GET"}]
    objs_orm_list = []
    acts_orm_list = []
    for i, obj in enumerate(objs_dict_list):
        if i < len(acts_dict_list):
            act_info = acts_dict_list[i]
            act_info.update({"create_by": create_by, "remark": remark})
            acts_orm_list.append(AdminAction(**act_info))
        obj.update({"create_by": create_by, "remark": remark})
        objs_orm_list.append(AdminObject(**obj))
    await AdminObject.bulk_create(objs_orm_list)
    pprint("AdminObject 资源 初始化成功")
    await AdminAction.bulk_create(acts_orm_list)
    pprint("AdminAction 行为 初始化成功")


async def close_db():
    # 关闭Tortoise ORM连接
    await Tortoise.close_connections()


# 执行初始化脚本
run_async(db_init())
run_async(init_admin_and_role())
# run_async(create_project_source_default())
# run_async(create_objs_acts_default())
run_async(close_db())
