from schemas import Token<PERSON>ayload
from models import AdminUser
from datetime import datetime, timedelta
from typing import Union, Any
from jose import jwt
from fastapi import Depends, Request
from fastapi.security import OAuth2PasswordBearer
from config import settings
from core.exce.base import To<PERSON><PERSON>xpired, UserError, AuthenticationError
from pydantic import ValidationError
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.APP.API_VERSION}/login-openapi"
)


async def get_current_user(
        token: str = Depends(reusable_oauth2)
) -> AdminUser:
    try:
        payload = jwt.decode(
            token, settings.JWT.SECRET_KEY, algorithms=[settings.JWT.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except jwt.ExpiredSignatureError:
        raise TokenExpired()
    except (jwt.JWTError, ValidationError):
        raise AuthenticationError()
    user = await AdminUser.get_or_none(id=token_data.sub)
    if not user:
        raise UserError('用户不存在')
    return user


async def get_current_active_user(
        current_user: AdminUser = Depends(get_current_user),
) -> AdminUser:
    if not current_user.is_active:
        raise UserError('用户未激活')
    return current_user


def create_access_token(
        subject: Union[str, Any], expires_delta: timedelta = None
) -> str:
    """
    生成token
    :param subject:需要存储到token的数据(注意token里面的数据，属于公开的)
    :param expires_delta:
    :return:
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.JWT.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, settings.JWT.SECRET_KEY, algorithm=settings.JWT.ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
