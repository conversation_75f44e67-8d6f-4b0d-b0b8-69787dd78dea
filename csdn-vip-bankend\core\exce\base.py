class UserError(Exception):
    def __init__(self, err_desc: str = "User Authentication Failed"):
        self.err_desc = err_desc


class TokenExpired(Exception):
    def __init__(self, err_desc: str = "Token has expired"):
        self.err_desc = err_desc


class AuthenticationError(Exception):
    def __init__(self, err_desc: str = "Permission denied"):
        self.err_desc = err_desc


class PasswordError(Exception):
    def __init__(self, err_desc: str = "密码验证失败"):
        self.err_desc = err_desc


class DouyinError(Exception):
    def __init__(self, err_desc: str = "douyin error"):
        self.err_desc = err_desc


class WechatError(Exception):
    def __init__(self, err_desc: str = "wechat error"):
        self.err_desc = err_desc


class GaodeError(Exception):
    def __init__(self, err_desc: str = "gaode error"):
        self.err_desc = err_desc


class NoVipError(Exception):
    def __init__(self, err_desc: str = "NoVip error"):
        self.err_desc = err_desc



