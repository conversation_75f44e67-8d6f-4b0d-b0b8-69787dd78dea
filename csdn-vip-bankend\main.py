import uvicorn
from fastapi import FastAPI, Depends
from tortoise.contrib.fastapi import register_tortoise
from config import settings
from core.exce.exception_handle import register_exception
from core.redis_client import register_redis
from fastapi.middleware.cors import CORSMiddleware
from tortoise import Tortoise
from utils.logger import logger, init_logger
import logging
import sentry_sdk

app = FastAPI(
    debug=settings.APP.DEBUG,
    title=settings.APP.TITLE,
    description=settings.APP.DESCRIPTION,
    docs_url=settings.APP.DOCS_URL,
    redoc_url=settings.APP.REDOC_URL,
)
print("初始化日志前")
# 初始化日志
init_logger()

# # debug分析工具
# if settings.APP.DEBUG:
#     from utils.debug_toolbar.middleware import DebugToolbarMiddleware
#
#     app.add_middleware(
#         DebugToolbarMiddleware,
#         panels=["utils.debug_toolbar.panels.tortoise.TortoisePanel"],
#     )
#     logger.info("Debug模式已打开！Debug toolbar 加载完成！")

# 跨域设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
print("初始化日志后1")
# 注册捕获全局异常
register_exception(app)
print("初始化日志后2")
# 请求拦截
# register_auth_mw(app)

# 全局的ORM注册
TORTOISE_ORM = {
    "connections": {
        "sqlite": f"sqlite://{settings.SQLITE.DATABASE}"
    },
    "apps": {
        "template": {
            # models对应上面创建的models.py
            "models": [
                "models",
                "aerich.models",
                "casbin_tortoise_adapter",
            ],
            "default_connection": "sqlite",
        },
    },
    'use_tz': False,
    'timezone': 'Asia/Shanghai'
}

register_tortoise(
    app,
    TORTOISE_ORM,
    generate_schemas=True
)
print("初始化日志后3")
# 注册redis
# register_redis(app)

# 设置默认连接的b1，如果注释掉数据库2和bi，这里也需要将下面默认的注释掉。
Tortoise.init_models(["models"], "template")
from api import routers

for router in routers:
    app.include_router(router, prefix=settings.APP.API_VERSION)
print("初始化日志后4")
if __name__ == "__main__":
    print("初始化日志后5")
    uvicorn.run('main:app', host='0.0.0.0', port=11211)  # , reload=True

