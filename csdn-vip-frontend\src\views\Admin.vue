<template>
  <div class="admin-container">
    <!-- 侧边栏 -->
    <div class="admin-sidebar">
      <div class="sidebar-header">
        <h2>CSDN提取系统</h2>
        <p>管理后台</p>
      </div>
      <el-menu 
        :default-active="activeMenu" 
        class="admin-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="dashboard">
          <el-icon><Odometer /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        <el-menu-item index="tokens">
          <el-icon><Key /></el-icon>
          <span>Token管理</span>
        </el-menu-item>
        <el-menu-item index="extraction-cores">
          <el-icon><Connection /></el-icon>
          <span>提取核心管理</span>
        </el-menu-item>
        <el-menu-item index="users">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </el-menu-item>
        <el-menu-item index="records">
          <el-icon><Document /></el-icon>
          <span>提取记录</span>
        </el-menu-item>
        <el-menu-item index="profile">
          <el-icon><UserFilled /></el-icon>
          <span>个人中心</span>
        </el-menu-item>
        <el-menu-item index="settings">
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </el-menu-item>
      </el-menu>
      
      <div class="sidebar-footer">
        <el-button @click="logout" style="width: 100%">
          <el-icon><SwitchButton /></el-icon>
          退出登录
        </el-button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
      <div class="admin-header">
        <div class="header-left">
          <h1>{{ getPageTitle() }}</h1>
        </div>
        <div class="header-right">
          <el-space>
            <el-text class="welcome-text">欢迎，管理员</el-text>
            <el-button circle @click="refreshData">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-space>
        </div>
      </div>

      <div class="admin-content">
        <!-- 仪表盘 -->
        <div v-show="activeMenu === 'dashboard'" class="dashboard-view">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <el-icon><DataAnalysis /></el-icon>
                    <span>系统概览</span>
                  </div>
                </template>
                
                <div class="overview-stats">
                  <div class="stat-item">
                    <div class="stat-value">{{ dashboardData.totalTokens }}</div>
                    <div class="stat-label">总访问令牌</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ dashboardData.todayExtractions }}</div>
                    <div class="stat-label">今日提取次数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ dashboardData.successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ dashboardData.activeUsers }}</div>
                    <div class="stat-label">活跃用户</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>最近提取记录</span>
                </template>
                <div v-if="recentRecords.length === 0" class="empty-placeholder">
                  <el-empty description="暂无最近记录" :image-size="100" />
                </div>
                <div v-else>
                  <el-timeline>
                    <el-timeline-item 
                      v-for="record in recentRecords" 
                      :key="record.id"
                      :timestamp="formatTime(record.request_time)"
                      placement="top"
                    >
                      <el-card class="timeline-card">
                        <div class="timeline-content">
                          <div class="timeline-title">{{ record.title || '未知标题' }}</div>
                          <div class="timeline-meta">
                            <el-tag :type="getStatusType(record.status)" size="small">
                              {{ getStatusText(record.status) }}
                            </el-tag>
                            <span class="timeline-author">{{ record.author || '未知作者' }}</span>
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>系统状态</span>
                </template>
                <div class="system-status">
                  <div class="status-item">
                    <span class="status-label">提取核心状态</span>
                    <el-tag :type="systemStatus.coreStatus ? 'success' : 'danger'">
                      {{ systemStatus.coreStatus ? '正常' : '异常' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-label">数据库连接</span>
                    <el-tag :type="systemStatus.dbStatus ? 'success' : 'danger'">
                      {{ systemStatus.dbStatus ? '正常' : '异常' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-label">缓存服务</span>
                    <el-tag :type="systemStatus.cacheStatus ? 'success' : 'danger'">
                      {{ systemStatus.cacheStatus ? '正常' : '异常' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-label">系统负载</span>
                    <el-progress 
                      :percentage="systemStatus.cpuUsage" 
                      :color="getProgressColor(systemStatus.cpuUsage)"
                    />
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- Token管理 -->
        <div v-show="activeMenu === 'tokens'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><Key /></el-icon>
                <span>Token管理</span>
              </div>
            </template>
            <el-empty description="Token管理功能正在开发中">
              <el-button type="primary">创建Token</el-button>
            </el-empty>
          </el-card>
        </div>
        
        <!-- 提取核心管理 -->
        <div v-show="activeMenu === 'extraction-cores'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>提取核心管理</span>
              </div>
            </template>
            <el-empty description="提取核心管理功能正在开发中">
              <el-button type="primary">添加核心</el-button>
            </el-empty>
          </el-card>
        </div>
        
        <!-- 用户管理 -->
        <div v-show="activeMenu === 'users'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><User /></el-icon>
                <span>用户管理</span>
              </div>
            </template>
            <el-empty description="用户管理功能正在开发中">
              <el-button type="primary">创建用户</el-button>
            </el-empty>
          </el-card>
        </div>
        
        <!-- 提取记录管理 -->
        <div v-show="activeMenu === 'records'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>提取记录管理</span>
              </div>
            </template>
            <el-empty description="提取记录管理功能正在开发中">
              <el-button type="primary">查看记录</el-button>
            </el-empty>
          </el-card>
        </div>
        
        <!-- 个人中心 -->
        <div v-show="activeMenu === 'profile'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><UserFilled /></el-icon>
                <span>个人中心</span>
              </div>
            </template>
            <el-empty description="个人中心功能正在开发中">
              <el-button type="primary">编辑资料</el-button>
            </el-empty>
          </el-card>
        </div>
        
        <!-- 系统设置 -->
        <div v-show="activeMenu === 'settings'" class="placeholder-view">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>系统设置</span>
              </div>
            </template>
            <el-empty description="系统设置功能正在开发中">
              <el-button type="primary">配置系统</el-button>
            </el-empty>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  SwitchButton, DataAnalysis, Key, Setting, Document, Odometer,
  Connection, User, UserFilled, Refresh
} from '@element-plus/icons-vue'

// 导入管理组件 - 暂时注释，稍后逐步添加
// import TokenManagement from '../components/admin/TokenManagement.vue'
// import ExtractionCoreManagement from '../components/admin/ExtractionCoreManagement.vue'
// import UserManagement from '../components/admin/UserManagement.vue'
// import RecordManagement from '../components/admin/RecordManagement.vue'
// import ProfileCenter from '../components/admin/ProfileCenter.vue'
// import SystemSettings from '../components/admin/SystemSettings.vue'

const router = useRouter()

// 当前活动菜单
const activeMenu = ref('dashboard')

// 仪表盘数据
const dashboardData = reactive({
  totalTokens: 0,
  todayExtractions: 0,
  successRate: 0,
  activeUsers: 0
})

// 最近记录
const recentRecords = ref<any[]>([])

// 系统状态
const systemStatus = reactive({
  coreStatus: true,
  dbStatus: true,
  cacheStatus: true,
  cpuUsage: 25
})

// 状态映射
const statusMap = {
  1: { text: '等待中', type: 'info' },
  2: { text: '处理中', type: 'warning' },
  3: { text: '成功', type: 'success' },
  4: { text: '失败', type: 'danger' },
  5: { text: '重复', type: 'warning' }
}

// 菜单标题映射
const menuTitleMap = {
  dashboard: '仪表盘',
  tokens: 'Token管理',
  'extraction-cores': '提取核心管理',
  users: '用户管理',
  records: '提取记录',
  profile: '个人中心',
  settings: '系统设置'
}

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  activeMenu.value = index
}

// 获取页面标题
const getPageTitle = () => {
  return menuTitleMap[activeMenu.value as keyof typeof menuTitleMap] || '管理后台'
}

// 获取状态文本
const getStatusText = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.text || '未知'
}

// 获取状态类型
const getStatusType = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 刷新数据
const refreshData = async () => {
  try {
    await loadDashboardData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('Refresh failed:', error)
    ElMessage.error('刷新失败')
  }
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    // 模拟数据，实际应调用API
    dashboardData.totalTokens = 156
    dashboardData.todayExtractions = 1234
    dashboardData.successRate = 87.5
    dashboardData.activeUsers = 45
    
    // 模拟最近记录
    recentRecords.value = [
      {
        id: 1,
        title: 'Spring Boot 入门教程',
        author: 'coder123',
        status: 3,
        request_time: new Date(Date.now() - 1000 * 60 * 5).toISOString()
      },
      {
        id: 2,
        title: 'Vue3 组合式API详解',
        author: 'frontend_dev',
        status: 3,
        request_time: new Date(Date.now() - 1000 * 60 * 15).toISOString()
      },
      {
        id: 3,
        title: 'Python数据分析实战',
        author: 'data_analyst',
        status: 4,
        request_time: new Date(Date.now() - 1000 * 60 * 30).toISOString()
      }
    ]
  } catch (error) {
    console.error('Load dashboard data failed:', error)
  }
}

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    localStorage.removeItem('admin_token')
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 页面初始化
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.admin-container {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
}

/* 侧边栏样式 */
.admin-sidebar {
  width: 250px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.sidebar-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.admin-menu {
  flex: 1;
  border: none;
  background: transparent;
}

.admin-menu .el-menu-item {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-menu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.admin-menu .el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 主内容区样式 */
.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header h1 {
  margin: 0;
  color: #303133;
  font-size: 1.5rem;
}

.welcome-text {
  color: #606266;
  margin-right: 1rem;
}

.admin-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* 仪表盘样式 */
.dashboard-view {
  height: 100%;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

/* 时间线样式 */
.timeline-card {
  margin-bottom: 1rem;
  border-radius: 8px;
}

.timeline-content {
  padding: 0.5rem;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #303133;
}

.timeline-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.timeline-author {
  color: #909399;
  font-size: 14px;
}

/* 系统状态样式 */
.system-status {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

/* 空状态样式 */
.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 占位符视图样式 */
.placeholder-view {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
    height: auto;
  }
  
  .admin-sidebar {
    width: 100%;
    min-height: auto;
  }
  
  .admin-content {
    padding: 1rem;
  }
  
  .overview-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .stat-item {
    padding: 1rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .timeline-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
