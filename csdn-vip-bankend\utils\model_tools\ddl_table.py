# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/6/9 10:42
# @File     : ddl_table.py
# @Project  : template
from sqlalchemy import inspect, create_engine


class DDLTable:
    def __init__(self, table_name: str, columns: list, data: list) -> None:
        self.table_name = table_name
        self.columns = columns
        self.data = data

    def create_table(self):
        """
        创建表
        """
        sql = f"CREATE TABLE {self.table_name} ("
        for column in self.columns:
            for key, value in column.items():
                sql += f"{key} {value},"
        sql = sql[:-1] + ");"
        return sql

    def insert_data(self):
        """
        插入数据
        """
        sql = f"INSERT INTO {self.table_name} VALUES "
        for row in self.data:
            sql += "("
            for key, value in row.items():
                if isinstance(value, str):
                    sql += f"'{value}',"
                else:
                    sql += f"{value},"
            sql = sql[:-1] + "),"
        sql = sql[:-1] + ";"
        return sql

    def drop_table(self):
        """
        删除表
        """
        sql = f"DROP TABLE IF EXISTS {self.table_name};"
        return sql

    def truncate_table(self):
        """
        清空表
        """
        sql = f"TRUNCATE TABLE {self.table_name};"
        return sql


# 获取数据库信息
class DBInfo:
    def __init__(self, database: str, user: str, password: str, host: str, port: str) -> None:
        self.database = database
        self.user = user
        self.password = password
        self.host = host
        self.port = port

    def get_engine(self):
        """
        获取数据库引擎
        """
        engine = create_engine(f"postgresql+psycopg2://"
                               f"{self.user}:"
                               f"{self.password}@"
                               f"{self.host}:"
                               f"{self.port}/"
                               f"{self.database}")
        return engine

    def get_inspect(self):
        """
        获取数据库信息
        """
        engine = self.get_engine()
        inspect_info = inspect(engine)
        return inspect_info

    def get_table_names(self):
        """
        获取数据库表名
        """
        inspect_info = self.get_inspect()
        table_names = inspect_info.get_table_names()
        return table_names

    def get_column_names(self, table_name: str):
        """
        获取数据库表的列名
        """
        inspect_info = self.get_inspect()
        column_names = inspect_info.get_columns(table_name)
        return column_names

    def get_column_names_list(self, table_name: str):
        """
        获取数据库表的列名
        """
        column_names = self.get_column_names(table_name)
        column_names_list = [column_name['name'] for column_name in column_names]
        return column_names_list

    def get_column_type(self, table_name: str):
        """
        获取数据库表的列类型
        """
        inspect_info = self.get_inspect()
        column_type = inspect_info.get_columns(table_name)
        return column_type

    def get_column_type_list(self, table_name: str):
        """
        获取数据库表的列类型
        """
        column_type = self.get_column_type(table_name)
        column_type_list = [column['type'] for column in column_type]
        return column_type_list

    def get_column_comment(self, table_name: str):
        """
        获取数据库表的列注释
        """
        inspect_info = self.get_inspect()
        column_comment = inspect_info.get_columns(table_name)
        return column_comment

    def get_column_comment_list(self, table_name: str):
        """
        获取数据库表的列注释
        """
        column_comment = self.get_column_comment(table_name)
        column_comment_list = [column['comment'] for column in column_comment]
        return column_comment_list

    def get_column_comment_dict(self, table_name: str):
        """
        获取数据库表的列注释
        """
        column_comment = self.get_column_comment(table_name)
        column_comment_dict = {column['name']: column['comment'] for column in column_comment}
        return column_comment_dict

    def get_column_comment_dict_list(self, table_name: str):
        """
        获取数据库表的列注释
        """
        column_comment = self.get_column_comment(table_name)
        column_comment_dict_list = [{column['name']: column['comment']} for column in column_comment]
        return column_comment_dict_list




