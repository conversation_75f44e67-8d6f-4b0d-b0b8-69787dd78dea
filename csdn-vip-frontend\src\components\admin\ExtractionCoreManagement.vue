<template>
  <div class="core-management">
    <div class="management-header">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加提取核心
      </el-button>
      <el-button @click="refreshCores">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button @click="testAllCores" :loading="isTesting">
        <el-icon><Monitor /></el-icon>
        测试所有核心
      </el-button>
    </div>

    <!-- 核心列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Connection /></el-icon>
          <span>提取核心列表</span>
          <div class="header-info">
            <span>共 {{ coreList.length }} 个核心</span>
          </div>
        </div>
      </template>

      <el-table :data="coreList" style="width: 100%" v-loading="isLoading">
        <el-table-column prop="name" label="核心名称" width="150">
          <template #default="scope">
            <div class="core-name">
              <el-tag size="small" :type="scope.row.is_primary ? 'danger' : 'info'">
                {{ scope.row.is_primary ? '主' : '备' }}
              </el-tag>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="endpoint" label="API端点" show-overflow-tooltip>
          <template #default="scope">
            <span class="endpoint-url">{{ scope.row.endpoint }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="auth_token" label="授权密钥" show-overflow-tooltip>
          <template #default="scope">
            <div class="token-cell">
              <span class="token-value">{{ maskToken(scope.row.auth_token) }}</span>
              <el-button text type="primary" size="small" @click="copyToken(scope.row.auth_token)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="response_time" label="响应时间" width="120">
          <template #default="scope">
            <span v-if="scope.row.response_time">
              {{ scope.row.response_time }}ms
            </span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_check" label="最后检查" width="180">
          <template #default="scope">
            <span v-if="scope.row.last_check">
              {{ formatTime(scope.row.last_check) }}
            </span>
            <span v-else class="text-muted">从未检查</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_requests" label="总请求数" width="120">
          <template #default="scope">
            {{ scope.row.total_requests.toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-space>
              <el-button 
                text 
                type="primary" 
                @click="testCore(scope.row)"
                :loading="scope.row.testing"
              >
                <el-icon><Monitor /></el-icon>
                测试
              </el-button>
              <el-button 
                text 
                type="warning"
                @click="setPrimary(scope.row)"
                :disabled="scope.row.is_primary"
              >
                设为主核心
              </el-button>
              <el-button text type="info" @click="editCore(scope.row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button text type="danger" @click="deleteCore(scope.row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑核心对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="isEditing ? '编辑提取核心' : '添加提取核心'" 
      width="600px"
    >
      <el-form :model="coreForm" :rules="coreRules" ref="coreFormRef" label-width="120px">
        <el-form-item label="核心名称" prop="name">
          <el-input v-model="coreForm.name" placeholder="请输入核心名称" />
        </el-form-item>
        
        <el-form-item label="API端点" prop="endpoint">
          <el-input 
            v-model="coreForm.endpoint" 
            placeholder="https://api.example.com/extract"
          />
        </el-form-item>
        
        <el-form-item label="授权密钥" prop="auth_token">
          <el-input 
            v-model="coreForm.auth_token" 
            type="password" 
            show-password
            placeholder="请输入授权密钥"
          />
        </el-form-item>
        
        <el-form-item label="超时时间" prop="timeout">
          <el-input-number 
            v-model="coreForm.timeout" 
            :min="5" 
            :max="300"
            placeholder="秒"
          />
          <span class="form-tip">秒（5-300秒）</span>
        </el-form-item>
        
        <el-form-item label="设为主核心">
          <el-switch v-model="coreForm.is_primary" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="coreForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="可选，用于描述该核心的用途"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCore" :loading="isSaving">
            {{ isEditing ? '保存' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 测试结果对话框 -->
    <el-dialog v-model="showTestDialog" title="核心测试结果" width="500px">
      <div class="test-result">
        <div class="test-item">
          <span class="test-label">核心名称：</span>
          <span>{{ testResult.name }}</span>
        </div>
        <div class="test-item">
          <span class="test-label">测试状态：</span>
          <el-tag :type="testResult.success ? 'success' : 'danger'">
            {{ testResult.success ? '测试成功' : '测试失败' }}
          </el-tag>
        </div>
        <div class="test-item">
          <span class="test-label">响应时间：</span>
          <span>{{ testResult.response_time }}ms</span>
        </div>
        <div class="test-item">
          <span class="test-label">测试消息：</span>
          <span>{{ testResult.message }}</span>
        </div>
        <div v-if="testResult.error" class="test-item">
          <span class="test-label">错误详情：</span>
          <el-text type="danger">{{ testResult.error }}</el-text>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Plus, Refresh, Monitor, Connection, CopyDocument, Edit, Delete
} from '@element-plus/icons-vue'

// 模拟数据
const coreList = ref([
  {
    id: 1,
    name: '主提取核心',
    endpoint: 'https://api-primary.example.com/extract',
    auth_token: 'core_abc123def456ghi789jkl012mno345pqr678',
    status: 'online',
    is_primary: true,
    response_time: 120,
    last_check: '2024-01-15T10:30:00',
    total_requests: 15234,
    description: '主要的文章提取核心',
    timeout: 30,
    testing: false
  },
  {
    id: 2,
    name: '备用核心A',
    endpoint: 'https://api-backup1.example.com/extract',
    auth_token: 'core_xyz789uvw456rst123opq890lmn567hij234',
    status: 'online',
    is_primary: false,
    response_time: 200,
    last_check: '2024-01-15T10:28:00',
    total_requests: 8976,
    description: '备用提取核心',
    timeout: 30,
    testing: false
  },
  {
    id: 3,
    name: '备用核心B',
    endpoint: 'https://api-backup2.example.com/extract',
    auth_token: 'core_qwe456tyu789iop012asd345fgh678jkl901',
    status: 'offline',
    is_primary: false,
    response_time: null,
    last_check: '2024-01-15T09:45:00',
    total_requests: 2156,
    description: '备用提取核心（当前离线）',
    timeout: 30,
    testing: false
  }
])

const isLoading = ref(false)
const isSaving = ref(false)
const isTesting = ref(false)
const showCreateDialog = ref(false)
const showTestDialog = ref(false)
const isEditing = ref(false)
const editingId = ref<number | null>(null)

// 表单数据
const coreForm = reactive({
  name: '',
  endpoint: '',
  auth_token: '',
  timeout: 30,
  is_primary: false,
  description: ''
})

const coreFormRef = ref<FormInstance>()

const coreRules: FormRules = {
  name: [
    { required: true, message: '请输入核心名称', trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  auth_token: [
    { required: true, message: '请输入授权密钥', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请设置超时时间', trigger: 'blur' },
    { type: 'number', min: 5, max: 300, message: '超时时间应在5-300秒之间', trigger: 'blur' }
  ]
}

// 测试结果
const testResult = reactive({
  name: '',
  success: false,
  response_time: 0,
  message: '',
  error: ''
})

// 状态映射
const statusMap = {
  online: { text: '在线', type: 'success' },
  offline: { text: '离线', type: 'danger' },
  maintenance: { text: '维护中', type: 'warning' }
}

// 获取状态文本
const getStatusText = (status: string) => {
  return statusMap[status as keyof typeof statusMap]?.text || '未知'
}

// 获取状态类型
const getStatusType = (status: string) => {
  return statusMap[status as keyof typeof statusMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 隐藏token
const maskToken = (token: string) => {
  if (token.length <= 8) return token
  return token.substring(0, 8) + '...' + token.substring(token.length - 8)
}

// 复制token
const copyToken = async (token: string) => {
  try {
    await navigator.clipboard.writeText(token)
    ElMessage.success('密钥已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 刷新核心列表
const refreshCores = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('Refresh cores failed:', error)
    ElMessage.error('刷新失败')
  } finally {
    isLoading.value = false
  }
}

// 测试单个核心
const testCore = async (core: any) => {
  core.testing = true
  
  try {
    // 模拟测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const success = Math.random() > 0.3 // 70%成功率
    const responseTime = Math.floor(Math.random() * 500) + 50
    
    core.status = success ? 'online' : 'offline'
    core.response_time = success ? responseTime : null
    core.last_check = new Date().toISOString()
    
    testResult.name = core.name
    testResult.success = success
    testResult.response_time = responseTime
    testResult.message = success ? '核心运行正常' : '核心连接失败'
    testResult.error = success ? '' : '连接超时或服务不可用'
    
    showTestDialog.value = true
    
    if (success) {
      ElMessage.success(`${core.name} 测试成功`)
    } else {
      ElMessage.error(`${core.name} 测试失败`)
    }
  } catch (error) {
    console.error('Test core failed:', error)
    ElMessage.error('测试失败')
  } finally {
    core.testing = false
  }
}

// 测试所有核心
const testAllCores = async () => {
  isTesting.value = true
  
  try {
    for (const core of coreList.value) {
      await testCore(core)
      await new Promise(resolve => setTimeout(resolve, 500)) // 间隔500ms
    }
    ElMessage.success('所有核心测试完成')
  } catch (error) {
    console.error('Test all cores failed:', error)
    ElMessage.error('批量测试失败')
  } finally {
    isTesting.value = false
  }
}

// 设置为主核心
const setPrimary = async (core: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${core.name}" 设置为主核心吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 取消其他核心的主核心状态
    coreList.value.forEach(c => {
      c.is_primary = false
    })
    
    // 设置当前核心为主核心
    core.is_primary = true
    
    ElMessage.success('主核心设置成功')
  } catch {
    // 用户取消
  }
}

// 编辑核心
const editCore = (core: any) => {
  isEditing.value = true
  editingId.value = core.id
  
  coreForm.name = core.name
  coreForm.endpoint = core.endpoint
  coreForm.auth_token = core.auth_token
  coreForm.timeout = core.timeout
  coreForm.is_primary = core.is_primary
  coreForm.description = core.description
  
  showCreateDialog.value = true
}

// 保存核心
const saveCore = async () => {
  if (!coreFormRef.value) return
  
  try {
    const valid = await coreFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSaving.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (isEditing.value && editingId.value) {
      // 编辑模式
      const index = coreList.value.findIndex(c => c.id === editingId.value)
      if (index > -1) {
        Object.assign(coreList.value[index], {
          name: coreForm.name,
          endpoint: coreForm.endpoint,
          auth_token: coreForm.auth_token,
          timeout: coreForm.timeout,
          is_primary: coreForm.is_primary,
          description: coreForm.description
        })
      }
      ElMessage.success('核心更新成功')
    } else {
      // 新建模式
      const newCore = {
        id: Date.now(),
        name: coreForm.name,
        endpoint: coreForm.endpoint,
        auth_token: coreForm.auth_token,
        timeout: coreForm.timeout,
        is_primary: coreForm.is_primary,
        description: coreForm.description,
        status: 'offline',
        response_time: null,
        last_check: null,
        total_requests: 0,
        testing: false
      }
      
      coreList.value.unshift(newCore)
      ElMessage.success('核心添加成功')
    }
    
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    console.error('Save core failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSaving.value = false
  }
}

// 删除核心
const deleteCore = async (core: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除核心 "${core.name}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    if (core.is_primary) {
      ElMessage.error('不能删除主核心，请先设置其他核心为主核心')
      return
    }
    
    const index = coreList.value.findIndex(c => c.id === core.id)
    if (index > -1) {
      coreList.value.splice(index, 1)
      ElMessage.success('核心已删除')
    }
  } catch {
    // 用户取消
  }
}

// 重置表单
const resetForm = () => {
  isEditing.value = false
  editingId.value = null
  
  coreForm.name = ''
  coreForm.endpoint = ''
  coreForm.auth_token = ''
  coreForm.timeout = 30
  coreForm.is_primary = false
  coreForm.description = ''
  
  coreFormRef.value?.resetFields()
}
</script>

<style scoped>
.core-management {
  height: 100%;
}

.management-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.header-info {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.core-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.endpoint-url {
  font-family: 'Courier New', monospace;
  color: #606266;
}

.token-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.token-value {
  font-family: 'Courier New', monospace;
  color: #606266;
}

.text-muted {
  color: #c0c4cc;
}

.form-tip {
  margin-left: 0.5rem;
  color: #909399;
  font-size: 14px;
}

.test-result {
  space-y: 1rem;
}

.test-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.test-label {
  font-weight: 500;
  color: #606266;
  width: 100px;
  flex-shrink: 0;
}
</style>
