from tortoise.fields.base import Field
from typing import Any


class AutoIntField(Field[int], int):
    """
    Auto INCREMENT Integer field. (32-bit signed)

    """

    SQL_TYPE = "INT"

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)

    @property
    def constraints(self) -> dict:
        return {
            "ge": 1 if self.generated or self.reference else -2147483648,
            "le": 2147483647,
        }

    class _db_postgres:
        SQL_TYPE = "SERIAL"

    class _db_sqlite:
        SQL_TYPE = "INTEGER AUTOINCREMENT "

    class _db_mysql:
        SQL_TYPE = "INT AUTO_INCREMENT"

    class _db_mssql:
        SQL_TYPE = "INT IDENTITY(1,1)"

    class _db_oracle:
        SQL_TYPE = "INT GENERATED BY DEFAULT AS IDENTITY"
