from fastapi import Request, Response
from fastapi.responses import JSONResponse
import re
import logging
from typing import List, Set
import time
from collections import defaultdict

logger = logging.getLogger(__name__)


class SecurityMiddleware:
    """安全中间件"""
    
    def __init__(self):
        # 恶意模式检测
        self.malicious_patterns = [
            # SQL注入模式
            r"(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)",
            r"(\b(or|and)\s+\d+\s*=\s*\d+)",
            r"(\b(or|and)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            
            # XSS模式
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            
            # 路径遍历
            r"\.\./",
            r"\.\.\\",
            
            # 命令注入
            r"[;&|`]",
            r"\$\([^)]*\)",
            
            # 其他恶意模式
            r"eval\s*\(",
            r"exec\s*\(",
            r"system\s*\(",
            r"shell_exec\s*\(",
        ]
        
        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.malicious_patterns]
        
        # 可疑IP追踪
        self.suspicious_ips = defaultdict(list)
        self.blocked_ips: Set[str] = set()
        
        # 请求频率追踪
        self.request_counts = defaultdict(list)
        
    async def __call__(self, request: Request, call_next):
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        
        # 检查IP是否被阻止
        if client_ip in self.blocked_ips:
            logger.warning(f"Blocked IP attempted access: {client_ip}")
            return JSONResponse(
                status_code=403,
                content={
                    "success": False,
                    "message": "访问被拒绝",
                    "error_code": "ACCESS_DENIED"
                }
            )
        
        # 安全检查
        security_check_result = await self._security_check(request, client_ip)
        if not security_check_result["allowed"]:
            return JSONResponse(
                status_code=security_check_result["status_code"],
                content={
                    "success": False,
                    "message": security_check_result["message"],
                    "error_code": security_check_result["error_code"]
                }
            )
        
        # 记录正常请求
        self._record_request(client_ip)
        
        # 继续处理请求
        try:
            response = await call_next(request)
            
            # 添加安全头部
            self._add_security_headers(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Request processing error for IP {client_ip}: {e}")
            raise
    
    async def _security_check(self, request: Request, client_ip: str) -> dict:
        """执行安全检查"""
        
        # 1. 检查User-Agent
        user_agent = request.headers.get("User-Agent", "")
        if self._is_suspicious_user_agent(user_agent):
            self._mark_suspicious_activity(client_ip, "suspicious_user_agent")
            return {
                "allowed": False,
                "status_code": 403,
                "message": "可疑的User-Agent",
                "error_code": "SUSPICIOUS_USER_AGENT"
            }
        
        # 2. 检查请求频率
        if self._is_high_frequency_request(client_ip):
            self._mark_suspicious_activity(client_ip, "high_frequency")
            return {
                "allowed": False,
                "status_code": 429,
                "message": "请求频率过高",
                "error_code": "HIGH_FREQUENCY"
            }
        
        # 3. 检查URL和参数中的恶意模式
        url_check = self._check_malicious_patterns(str(request.url))
        if url_check["is_malicious"]:
            self._mark_suspicious_activity(client_ip, f"malicious_url: {url_check['pattern']}")
            return {
                "allowed": False,
                "status_code": 400,
                "message": "检测到恶意请求模式",
                "error_code": "MALICIOUS_PATTERN"
            }
        
        # 4. 检查请求体（如果有）
        if hasattr(request, "_body") and request._body:
            try:
                body = request._body.decode('utf-8')
                body_check = self._check_malicious_patterns(body)
                if body_check["is_malicious"]:
                    self._mark_suspicious_activity(client_ip, f"malicious_body: {body_check['pattern']}")
                    return {
                        "allowed": False,
                        "status_code": 400,
                        "message": "请求内容包含恶意模式",
                        "error_code": "MALICIOUS_CONTENT"
                    }
            except Exception as e:
                logger.debug(f"Body check error: {e}")
        
        # 5. 检查请求头
        headers_check = self._check_request_headers(request.headers)
        if not headers_check["allowed"]:
            self._mark_suspicious_activity(client_ip, headers_check["reason"])
            return {
                "allowed": False,
                "status_code": 400,
                "message": headers_check["message"],
                "error_code": "INVALID_HEADERS"
            }
        
        return {"allowed": True}
    
    def _check_malicious_patterns(self, text: str) -> dict:
        """检查文本中的恶意模式"""
        for i, pattern in enumerate(self.compiled_patterns):
            if pattern.search(text):
                return {
                    "is_malicious": True,
                    "pattern": self.malicious_patterns[i]
                }
        return {"is_malicious": False}
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """检查是否为可疑的User-Agent"""
        if not user_agent:
            return True
        
        # 检查常见的爬虫/攻击工具特征
        suspicious_agents = [
            "sqlmap", "nmap", "nikto", "dirb", "gobuster", "wpscan",
            "python-requests", "curl/", "wget/", "HTTPie",
            "bot", "crawler", "spider", "scraper"
        ]
        
        user_agent_lower = user_agent.lower()
        for agent in suspicious_agents:
            if agent in user_agent_lower:
                return True
        
        # 检查过短的User-Agent
        if len(user_agent) < 10:
            return True
        
        return False
    
    def _is_high_frequency_request(self, client_ip: str, window_seconds: int = 10, max_requests: int = 20) -> bool:
        """检查是否为高频请求"""
        current_time = time.time()
        request_times = self.request_counts[client_ip]
        
        # 清理过期记录
        cutoff_time = current_time - window_seconds
        self.request_counts[client_ip] = [t for t in request_times if t > cutoff_time]
        
        # 检查频率
        return len(self.request_counts[client_ip]) >= max_requests
    
    def _check_request_headers(self, headers) -> dict:
        """检查请求头"""
        # 检查Content-Length是否合理
        content_length = headers.get("Content-Length")
        if content_length:
            try:
                length = int(content_length)
                if length > 10 * 1024 * 1024:  # 10MB限制
                    return {
                        "allowed": False,
                        "message": "请求内容过大",
                        "reason": "large_content"
                    }
            except ValueError:
                return {
                    "allowed": False,
                    "message": "无效的Content-Length",
                    "reason": "invalid_content_length"
                }
        
        # 检查Host头
        host = headers.get("Host")
        if host and self._is_suspicious_host(host):
            return {
                "allowed": False,
                "message": "可疑的Host头",
                "reason": "suspicious_host"
            }
        
        return {"allowed": True}
    
    def _is_suspicious_host(self, host: str) -> bool:
        """检查是否为可疑的Host"""
        # 检查IP地址直接访问
        if re.match(r'^\d+\.\d+\.\d+\.\d+', host):
            return False  # 暂时允许IP访问
        
        # 检查可疑域名模式
        suspicious_patterns = [
            r'.*\.onion$',  # Tor域名
            r'.*\.bit$',    # Namecoin域名
        ]
        
        for pattern in suspicious_patterns:
            if re.match(pattern, host, re.IGNORECASE):
                return True
        
        return False
    
    def _mark_suspicious_activity(self, client_ip: str, reason: str):
        """标记可疑活动"""
        current_time = time.time()
        self.suspicious_ips[client_ip].append({
            "time": current_time,
            "reason": reason
        })
        
        # 清理过期记录
        cutoff_time = current_time - 3600  # 1小时
        self.suspicious_ips[client_ip] = [
            activity for activity in self.suspicious_ips[client_ip]
            if activity["time"] > cutoff_time
        ]
        
        # 如果可疑活动过多，阻止IP
        if len(self.suspicious_ips[client_ip]) >= 5:
            self.blocked_ips.add(client_ip)
            logger.warning(f"Blocked IP due to suspicious activity: {client_ip}")
        
        logger.warning(f"Suspicious activity from {client_ip}: {reason}")
    
    def _record_request(self, client_ip: str):
        """记录正常请求"""
        current_time = time.time()
        self.request_counts[client_ip].append(current_time)
        
        # 限制记录数量
        if len(self.request_counts[client_ip]) > 100:
            self.request_counts[client_ip] = self.request_counts[client_ip][-50:]
    
    def _add_security_headers(self, response: Response):
        """添加安全头部"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 尝试从代理头部获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接连接IP
        return request.client.host if request.client else "unknown"
    
    def unblock_ip(self, ip: str):
        """手动解除IP阻止"""
        if ip in self.blocked_ips:
            self.blocked_ips.remove(ip)
            if ip in self.suspicious_ips:
                del self.suspicious_ips[ip]
            logger.info(f"Unblocked IP: {ip}")
    
    def get_blocked_ips(self) -> List[str]:
        """获取被阻止的IP列表"""
        return list(self.blocked_ips)
    
    def get_suspicious_activities(self) -> dict:
        """获取可疑活动记录"""
        return dict(self.suspicious_ips)


# 全局安全中间件实例
security_middleware = SecurityMiddleware()
