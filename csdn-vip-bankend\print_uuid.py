# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/6/6 15:47
# @File     : print_uuid.py
# @Project  : template


# import uuid
#
# print(uuid.uuid4())


# 序列化

from pydantic import BaseModel, Extra


class MyModel(BaseModel):
    field1: str
    field2: int

    class Config:
        extra = 'allow'    # 只有为allow时才会反序列化时接收额外的字段


# 创建模型对象
data = {
    'field1': 'value1',
    'field2': 42,
    'field3': 'extra field'
}

# 反序列化数据到模型对象
model = MyModel(**data)

print(model.field1, "aa")
# 输出: 默认值 None
print(model.field2, "bb")
# 输出: 42
print(model.field3, "cc")
# 抛出异常: pydantic.error_wrappers.ValidationError: extra fields not permitted

# 序列化模型对象
serialized_data = model.json()
print(serialized_data, "dd")
# 输出: {"field2": 42}
