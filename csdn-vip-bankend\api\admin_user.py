from core.auth.auth_casbin import get_casbin_rule_user
from core.exce.response_code import HttpStatus
from core.response import ResultResponse, ListResult
from models import (
    AdminUser, AdminRole, AdminObject, AdminAction,
)
from schemas import (
    AdminUserOut, AdminUserUpdate, AdminUserCreate, AdminUserPasswordUpdate, AdminUserActiveUpdate, AdminUserDetail,
    AdminUserListQuery
)
from fastapi import Depends

from utils.datetime.tztime import get_sh_tz_time

from utils.base import ignore_none
from utils.fastapi_crudrouter import TortoiseCRUDRouter
from utils.fastapi_crudrouter.core import NOT_FOUND
from core.auth.base import get_current_active_user, get_password_hash, verify_password
from core.auth.auth_casbin import get_casbin

admin_user_router = TortoiseCRUDRouter(
    db_model=AdminUser,
    list_schema=AdminUserOut,
    create_schema=AdminUserCreate,
    update_schema=AdminUserUpdate,
    prefix="/admin-user",
    tags=["后台管理用户"],
    get_all_route=False,
    get_one_route=False,
    create_route=False,
    update_route=[Depends(get_current_active_user)],
    delete_one_route=[Depends(get_current_active_user)],
)


@admin_user_router.get(
    "",
    name="Get All",
    summary="Get All",
    response_model=ResultResponse[ListResult[AdminUserDetail]],
    dependencies=[Depends(get_casbin_rule_user)]
)
async def get_admin_user_list(query_params: AdminUserListQuery = Depends()) -> ResultResponse[ListResult[AdminUserDetail]]:
    skip, limit, username, create_by = query_params.skip, query_params.limit, query_params.username, query_params.create_by

    admin_user_list = await AdminUser.filter(is_deleted=False, **ignore_none(username__icontains=username, create_by__icontains=create_by)).all().order_by("id").values()
    if not admin_user_list:
        raise NOT_FOUND
    else:
        result_list = []
        # 获取用户的 角色
        e = await get_casbin()
        for admin_user in admin_user_list:
            policies_list = []
            user_role_dict = {}  # {'admin': 1, 'blbl': 2}
            roles = await e.get_roles_for_user(admin_user.get("username"))
            if "super_role" in roles:
                all_obj = await AdminObject.all().values("obj", "type")
                all_act = await AdminAction.all().values("act")
                role_policies = []
                for user_role in roles:
                    user_role_infos = await AdminRole.get_or_none(role=user_role)
                    if user_role_infos:
                        user_role_dict.update({"role": user_role_infos.role, "role_id": user_role_infos.id})

                    for obj in all_obj:
                        if obj.get('type') == 'api':
                            act_list = ["GET", "POST", "PUT", "DELETE"]
                            # for act in all_act:
                            for act in act_list:
                                role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])
                        elif obj.get('type') == 'menu':
                            act_list = ["SHOW"]
                            for act in act_list:
                                role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])
                policies_list = role_policies
            else:
                for role in roles:
                    # 获取角色的 policies
                    role_info = await AdminRole.get_or_none(role=role)
                    if role_info:
                        role_id = role_info.id
                        role_role = role_info.role
                        user_role_dict.update({"role": role_role, "role_id": role_id})
                        role_policies = await e.get_permissions_for_user_or_role(role)
                        for role_policy in role_policies:
                            if len(role_policy) >= 4:  # 因为不刷新接口再次请求的时候，列表在内存中是存在的，只会一直在后面追加元素。可能是 框架的bug。所以加了这个判断。
                                # 删除第四位和第四位后面的数据
                                del role_policy[3:]
                            obj_info = await AdminObject.filter(obj=role_policy[1]).values()
                            if obj_info:
                                obj_type = obj_info[0].get('type')
                                role_policy.append(obj_type)
                            else:
                                role_policy.append(None)
                            policies_list.append(role_policy)

                # await current_user.fetch_related('admin_user_process')
            admin_user.update({'user_roles': user_role_dict, 'policies': policies_list})
            exclude_keys = ['create_time', 'update_time', 'is_deleted', 'hashed_password', 'is_super']
            for key in exclude_keys:
                admin_user.pop(key)
            result = AdminUserDetail.parse_obj(admin_user)
            result_list.append(result)
        all_count = len(result_list)
        # 切片 查询的结果 result_list[skip: skip + limit]
        if limit:
            data = result_list[skip: skip + limit]
        else:
            data = result_list[skip:]
        return ResultResponse[ListResult[AdminUserDetail]](result=ListResult[AdminUserDetail](
            all_count=all_count,
            data=data
        ))


@admin_user_router.get(
    "/{item_id}",
    name="Get One",
    summary="Get One",
    response_model=ResultResponse[AdminUserDetail],
    dependencies=[Depends(get_current_active_user)]
)
async def get_admin_user_detail(item_id: int) -> ResultResponse[AdminUserDetail]:
    admin_user = await AdminUser.filter(id=item_id, is_deleted=False).first().values()
    if not admin_user:
        raise NOT_FOUND
    else:
        policies_list = []
        user_role_dict = {}  # {'admin': 1, 'blbl': 2}
        # 获取用户的 角色
        e = await get_casbin()
        roles = await e.get_roles_for_user(admin_user.get("username"))
        if "super_role" in roles:
            all_obj = await AdminObject.all().values("obj", "type")
            all_act = await AdminAction.all().values("act")
            role_policies = []
            for user_role in roles:
                user_role_infos = await AdminRole.get_or_none(role=user_role)
                if user_role_infos:
                    user_role_dict.update({"role": user_role_infos.role, "role_id": user_role_infos.id})

                for obj in all_obj:
                    if obj.get('type') == 'api':
                        act_list = ["GET", "POST", "PUT", "DELETE"]
                        # for act in all_act:
                        for act in act_list:
                            role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])
                    elif obj.get('type') == 'menu':
                        act_list = ["SHOW"]
                        for act in act_list:
                            role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])
            policies_list = role_policies
        else:
            for role in roles:
                # 获取角色的 policies
                role_info = await AdminRole.get_or_none(role=role)
                if role_info:
                    role_id = role_info.id
                    role_role = role_info.role
                    user_role_dict.update({"role": role_role, "role_id": role_id})
                    role_policies = await e.get_permissions_for_user_or_role(role)
                    for role_policy in role_policies:
                        if len(role_policy) >= 4:  # 因为不刷新接口再次请求的时候，列表在内存中是存在的，只会一直在后面追加元素。可能是 框架的bug。所以加了这个判断。
                            # 删除第四位和第四位后面的数据
                            del role_policy[3:]
                        obj_info = await AdminObject.filter(obj=role_policy[1]).values()
                        if obj_info:
                            obj_type = obj_info[0].get('type')
                            role_policy.append(obj_type)
                        else:
                            role_policy.append(None)
                        policies_list.append(role_policy)

            # await current_user.fetch_related('admin_user_process')
        admin_user.update({'user_roles': user_role_dict, 'policies': policies_list})
        exclude_keys = ['create_time', 'update_time', 'is_deleted', 'hashed_password', 'is_super']
        for key in exclude_keys:
            admin_user.pop(key)
        result = AdminUserDetail.parse_obj(admin_user)
        return ResultResponse[AdminUserDetail](result=result)


@admin_user_router.post(
    "",
    name="Create One",
    summary="Create One",
    response_model=ResultResponse[AdminUserOut],
    dependencies=[Depends(get_casbin_rule_user)]
)
async def create_admin_user(user_info: AdminUserCreate, current_user: AdminUser = Depends(get_current_active_user)) -> \
ResultResponse[AdminUserOut]:
    username = current_user.username
    user_info_dict = user_info.dict(exclude={'password', 'confirm'})
    user_info_dict['create_by'] = username
    user_info_dict['update_by'] = username
    admin_user = await AdminUser.create(
        hashed_password=get_password_hash(user_info.password),
        **user_info_dict
    )
    # default_role
    e = await get_casbin()
    res = await e.add_role_for_user(admin_user.username, "default_role")
    # admin_user_resource_orm = await AdminUserResource.create(admin_user_id=admin_user.id)
    admin_user = AdminUserOut.from_orm(admin_user)
    return ResultResponse[AdminUserOut](result=admin_user)


@admin_user_router.put(
    "-active/{item_id}",
    name="Change Status",
    summary="Change Status",
    response_model=ResultResponse[str],
    dependencies=[Depends(get_current_active_user)]
)
async def create_admin_user(item_id: int, user_info: AdminUserActiveUpdate, current_user: AdminUser = Depends(get_current_active_user)) -> ResultResponse[str]:
    username = current_user.username
    user_info_dict = user_info.dict(exclude={'password', 'confirm'})
    user_info_dict['update_by'] = username
    user_info_dict['update_time'] = get_sh_tz_time()
    result = await AdminUser.filter(id=item_id, is_deleted=False).update(
        is_active=user_info.is_active)

    if result == 0:
        raise NOT_FOUND
    else:
        return ResultResponse[str](result='状态修改成功')



@admin_user_router.put(
    "-password/{item_id}",
    name="Change Password",
    summary="Change Password",
    response_model=ResultResponse[str],
    dependencies=[Depends(get_current_active_user)]
)
async def create_admin_user(item_id: int, user_info: AdminUserPasswordUpdate) -> ResultResponse[str]:
    admin_user = await AdminUser.get_or_none(id=item_id, is_deleted=False)
    if not admin_user:
        raise NOT_FOUND
    try:
        verify_password(user_info.old_password, admin_user.hashed_password)
    except:
        return ResultResponse[str](code=403, message='密码不符合规范，请大小写加数字')
    if not verify_password(user_info.old_password, admin_user.hashed_password):
        # raise PasswordError()
        return ResultResponse[str](code=403, message='旧密码校验失败')
        # return ResultResponse[str](result='修改密码成功')
    await AdminUser.filter(id=item_id, is_deleted=False).update(
        hashed_password=get_password_hash(user_info.password))
    return ResultResponse[str](result='修改密码成功')


@admin_user_router.get(
    "-info",
    name="NowUserInfo",
    summary="NowUserInfo",
    response_model=ResultResponse[AdminUserDetail]
)
async def get_admin_user_info(
        current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[AdminUserDetail]:
    # 验证用户是否存在
    current_user_id = current_user.id
    current_user_name = current_user.username

    current_user_orm = await AdminUser.get_or_none(id=current_user_id)
    if not current_user_orm:
        return ResultResponse(code=HttpStatus.HTTP_404, message='用户不存在')
    e = await get_casbin()

    policies_list = []
    # # 获取用户的 policies
    # user_policies = await e.get_permissions_for_user_or_role(current_user_name)
    # if user_policies:
    #     for user_policy in user_policies:
    #         if len(user_policy) >= 4:  # 因为不刷新接口再次请求的时候，列表在内存中是存在的，只会一直在后面追加元素。可能是 框架的bug。所以加了这个判断。
    #             # 删除第四位和第四位后面的数据
    #             del user_policy[3:]
    #         obj_info = await AdminObject.filter(obj=user_policy[1]).values()
    #         if obj_info:
    #             obj_type = obj_info[0].get('type')
    #             user_policy.append(obj_type)
    #         else:
    #             user_policy.append(None)
    #         policies_list.append(user_policy)

    # 获取用户的角色组 user_role
    user_role_dict = {}  # {'admin': 1, 'blbl': 2}

    user_role = await e.get_roles_for_user(current_user_name)
    if user_role:
        if "super_role" in user_role:
            all_obj = await AdminObject.all().values("obj", "type")
            all_act = await AdminAction.all().values("act")

            user_role_list = user_role
            role_policies = []

            for user_role in user_role_list:
                user_role_infos = await AdminRole.get_or_none(role=user_role)
                if user_role_infos:
                    user_role_dict.update({"role": user_role_infos.role, "role_id": user_role_infos.id})

                for obj in all_obj:
                    if obj.get('type') == 'api':
                        act_list = ["GET", "POST", "PUT", "DELETE"]
                        # for act in all_act:
                        for act in act_list:
                            role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])
                    elif obj.get('type') == 'menu':
                        act_list = ["SHOW"]
                        for act in act_list:
                            role_policies.append([user_role, obj.get('obj'), act, obj.get('type')])

            policies_list = role_policies


        else:
            user_role_list = user_role

            # 获取用户组的 policies
            for user_role in user_role_list:
                user_role_infos = await AdminRole.get_or_none(role=user_role).values()
                if user_role_infos:
                    role_id = user_role_infos.get('id')
                    role_role = user_role_infos.get('role')
                    user_role_dict.update({"role": role_role, "role_id": role_id})
                    role_orm = await AdminRole.get_or_none(id=role_id)
                    e = await get_casbin()
                    role_policies = await e.get_permissions_for_user_or_role(role_orm.role)
                    for role_policy in role_policies:
                        if len(role_policy) >= 4:  # 因为不刷新接口再次请求的时候，列表在内存中是存在的，只会一直在后面追加元素。可能是 框架的bug。所以加了这个判断。
                            # 删除第四位和第四位后面的数据
                            del role_policy[3:]
                        obj_info = await AdminObject.filter(obj=role_policy[1]).values()
                        if obj_info:
                            obj_type = obj_info[0].get('type')
                            role_policy.append(obj_type)
                        else:
                            role_policy.append(None)
                        policies_list.append(role_policy)

    # await current_user.fetch_related('admin_user_process')
    current_user = AdminUserOut.from_orm(current_user).dict()
    current_user.update({'user_roles': user_role_dict, 'policies': policies_list})
    result = AdminUserDetail.parse_obj(current_user)
    return ResultResponse[AdminUserDetail](result=result)
