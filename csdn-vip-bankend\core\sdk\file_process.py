import zipfile
import os
import shutil
from .tencent_cloud import get_cos
from qcloud_cos.cos_exception import CosClientError, CosServiceError
from config import settings


def download_file(key, file_path):
    client = get_cos()
    for i in range(0, 10):
        try:
            response = client.download_file(
                Bucket=settings.TENCENT.COS.BUCKET,
                Key=key,
                DestFilePath=file_path)
            break
        except CosClientError or CosServiceError as e:
            print(e)

def upload_file(file_path, key):
    client = get_cos()
    # 使用高级接口断点续传，失败重试时不会上传已成功的分块(这里重试10次)
    for i in range(0, 10):
        try:
            response = client.upload_file(
            Bucket=settings.TENCENT.COS.BUCKET,
            Key=key,
            LocalFilePath=file_path)
            break
        except CosClientError or CosServiceError as e:
            print(e)

def unzip_file(zip_path, target_path):
    zip_file = zipfile.ZipFile(zip_path)
    zip_file.extractall(target_path)
    zip_file.close()

def clear_dir(path):
    shutil.rmtree(path)
    os.mkdir(path)

def remove_dir(path):
    shutil.rmtree(path)

def copy_dir(path, target_path):
    shutil.copytree(path, target_path)

def copy_file(file, target_path):
    shutil.copyfile(file, target_path)

def get_filelist(path):
    file_list = []
    for home, dirs, files in os.walk(path):
        for filename in files:
            # 文件名列表，包含完整路径
            file_list.append(os.path.join(home, filename))
            # # 文件名列表，只包含文件名
            # Filelist.append( filename)
    return file_list


def write_config(task_api, node_id, inoutput):
    params = task_api.get_params_by_id(node_id)
    with open(f'runner_{node_id}/dps/config/operator.toml',"w") as f:
        f.write(f'global_params.task_id = "{task_api.task_id}"\n')
        f.write(f'global_params.task_name = "{task_api.task_name}"\n')
        f.write(f'global_params.flow_id = "{task_api.flow_id}"\n')
        f.write(f'global_params.flow_version = "{task_api.flow_version}"\n')
        f.write(f'global_params.flow_name = "{task_api.flow_name}"\n')
        f.write(f'global_params.project_id = "{task_api.project_id}"\n')
        f.write(f'global_params.project_name = "{task_api.project_name}"\n')
        f.write('\n')
        for in_put in inoutput['input']:
            f.write(f'inputs.{in_put["name"]} = \"input/{in_put["file"]}\"\n')
        for out_put in inoutput['output']:
            f.write(f'outputs.{out_put["name"]} = \"output/{out_put["file"]}\"\n')
        f.write('\n')
        for param in params:
            f.write(f'params.{param["key"]} = \"\"\"{param["value"]}\"\"\"\n')