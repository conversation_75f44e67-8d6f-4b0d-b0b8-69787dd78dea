import api from './index'

// 管理员API接口类型定义
export interface AdminLoginRequest {
  username: string
  password: string
}

export interface AdminLoginResponse {
  token: string
  user: AdminUser
}

export interface AdminUser {
  id: number
  username: string
  email: string
  role: string
  status: boolean
  last_login?: string
  created_at: string
}

export interface TokenInfo {
  id: number
  token: string
  type: 'time' | 'count'
  remaining_count?: number
  expiry_time?: string
  status: boolean
  created_at: string
  description?: string
}

export interface CreateTokenRequest {
  type: 'time' | 'count'
  expiry_days?: number
  max_count?: number
  description?: string
}

export interface ExtractionCore {
  id: number
  name: string
  endpoint: string
  auth_token: string
  status: 'online' | 'offline' | 'maintenance'
  is_primary: boolean
  response_time?: number
  last_check?: string
  total_requests: number
  description?: string
  timeout: number
}

export interface CreateCoreRequest {
  name: string
  endpoint: string
  auth_token: string
  timeout: number
  is_primary: boolean
  description?: string
}

export interface DashboardStats {
  totalTokens: number
  todayExtractions: number
  successRate: number
  activeUsers: number
}

export interface SystemStatus {
  coreStatus: boolean
  dbStatus: boolean
  cacheStatus: boolean
  cpuUsage: number
}

// 管理员API接口
export const adminApi = {
  // 认证相关
  login: (data: AdminLoginRequest) => {
    return api.post<AdminLoginResponse>('/admin/login', data)
  },

  logout: () => {
    return api.post('/admin/logout')
  },

  refreshToken: () => {
    return api.post('/admin/refresh-token')
  },

  // 仪表盘
  getDashboardStats: () => {
    return api.get<DashboardStats>('/admin/dashboard/stats')
  },

  getSystemStatus: () => {
    return api.get<SystemStatus>('/admin/dashboard/system-status')
  },

  getRecentRecords: () => {
    return api.get('/admin/dashboard/recent-records')
  },

  // Token管理
  getTokenList: (params?: { page?: number; page_size?: number }) => {
    return api.get('/admin/tokens', { params })
  },

  createToken: (data: CreateTokenRequest) => {
    return api.post<TokenInfo>('/admin/tokens', data)
  },

  updateToken: (id: number, data: Partial<TokenInfo>) => {
    return api.put(`/admin/tokens/${id}`, data)
  },

  deleteToken: (id: number) => {
    return api.delete(`/admin/tokens/${id}`)
  },

  toggleTokenStatus: (id: number) => {
    return api.patch(`/admin/tokens/${id}/toggle-status`)
  },

  // 提取核心管理
  getCoreList: () => {
    return api.get<ExtractionCore[]>('/admin/cores')
  },

  createCore: (data: CreateCoreRequest) => {
    return api.post<ExtractionCore>('/admin/cores', data)
  },

  updateCore: (id: number, data: Partial<ExtractionCore>) => {
    return api.put(`/admin/cores/${id}`, data)
  },

  deleteCore: (id: number) => {
    return api.delete(`/admin/cores/${id}`)
  },

  testCore: (id: number) => {
    return api.post(`/admin/cores/${id}/test`)
  },

  setPrimaryCore: (id: number) => {
    return api.patch(`/admin/cores/${id}/set-primary`)
  },

  // 用户管理
  getUserList: (params?: { 
    page?: number
    page_size?: number
    search?: string
    role?: string
    status?: boolean 
  }) => {
    return api.get('/admin/users', { params })
  },

  createUser: (data: {
    username: string
    email: string
    password: string
    role: string
    status: boolean
    description?: string
  }) => {
    return api.post<AdminUser>('/admin/users', data)
  },

  updateUser: (id: number, data: Partial<AdminUser>) => {
    return api.put(`/admin/users/${id}`, data)
  },

  deleteUser: (id: number) => {
    return api.delete(`/admin/users/${id}`)
  },

  toggleUserStatus: (id: number) => {
    return api.patch(`/admin/users/${id}/toggle-status`)
  },

  getUserDetail: (id: number) => {
    return api.get(`/admin/users/${id}`)
  },

  // 提取记录管理
  getRecordList: (params?: {
    page?: number
    page_size?: number
    status?: number
    start_time?: string
    end_time?: string
    token?: string
    search?: string
  }) => {
    return api.get('/admin/records', { params })
  },

  deleteRecord: (id: number) => {
    return api.delete(`/admin/records/${id}`)
  },

  batchDeleteRecords: (ids: number[]) => {
    return api.delete('/admin/records/batch', { data: { ids } })
  },

  retryExtraction: (id: number) => {
    return api.post(`/admin/records/${id}/retry`)
  },

  exportRecords: (params?: {
    status?: number
    start_time?: string
    end_time?: string
    format?: 'csv' | 'excel'
  }) => {
    return api.get('/admin/records/export', { params, responseType: 'blob' })
  },

  cleanupRecords: (days: number) => {
    return api.delete(`/admin/records/cleanup`, { data: { days } })
  },

  // 个人中心
  getProfile: () => {
    return api.get('/admin/profile')
  },

  updateProfile: (data: {
    email?: string
    real_name?: string
    phone?: string
    department?: string
    bio?: string
  }) => {
    return api.put('/admin/profile', data)
  },

  updatePassword: (data: {
    current_password: string
    new_password: string
  }) => {
    return api.put('/admin/profile/password', data)
  },

  uploadAvatar: (file: File) => {
    const formData = new FormData()
    formData.append('avatar', file)
    return api.post('/admin/profile/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  getLoginHistory: () => {
    return api.get('/admin/profile/login-history')
  },

  logoutAllSessions: () => {
    return api.post('/admin/profile/logout-all-sessions')
  },

  getApiKeys: () => {
    return api.get('/admin/profile/api-keys')
  },

  generateApiKey: (name: string) => {
    return api.post('/admin/profile/api-keys', { name })
  },

  deleteApiKey: (id: number) => {
    return api.delete(`/admin/profile/api-keys/${id}`)
  },

  updateNotificationSettings: (settings: any) => {
    return api.put('/admin/profile/notification-settings', settings)
  },

  // 系统设置
  getSystemSettings: () => {
    return api.get('/admin/settings')
  },

  updateBasicSettings: (data: any) => {
    return api.put('/admin/settings/basic', data)
  },

  updateExtractionSettings: (data: any) => {
    return api.put('/admin/settings/extraction', data)
  },

  updateSecuritySettings: (data: any) => {
    return api.put('/admin/settings/security', data)
  },

  updateRateLimitSettings: (data: any) => {
    return api.put('/admin/settings/rate-limit', data)
  },

  updateEmailSettings: (data: any) => {
    return api.put('/admin/settings/email', data)
  },

  testEmailSettings: () => {
    return api.post('/admin/settings/email/test')
  },

  updateLoggingSettings: (data: any) => {
    return api.put('/admin/settings/logging', data)
  },

  downloadLogs: () => {
    return api.get('/admin/settings/logs/download', { responseType: 'blob' })
  },

  clearLogs: () => {
    return api.delete('/admin/settings/logs')
  },

  // 统计分析
  getStatistics: (params?: {
    start_date?: string
    end_date?: string
    granularity?: 'hour' | 'day' | 'week' | 'month'
  }) => {
    return api.get('/admin/statistics', { params })
  }
}

export default adminApi
