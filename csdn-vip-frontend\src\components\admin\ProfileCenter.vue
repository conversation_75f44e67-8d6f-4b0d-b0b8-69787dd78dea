<template>
  <div class="profile-center">
    <el-row :gutter="20">
      <!-- 个人信息卡片 -->
      <el-col :span="8">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <el-icon><UserFilled /></el-icon>
              <span>个人信息</span>
            </div>
          </template>
          
          <div class="profile-info">
            <div class="avatar-section">
              <el-avatar :size="80" :src="userInfo.avatar">
                {{ userInfo.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <el-button text type="primary" @click="showAvatarDialog = true">
                更换头像
              </el-button>
            </div>
            
            <div class="info-section">
              <div class="info-item">
                <span class="label">用户名：</span>
                <span class="value">{{ userInfo.username }}</span>
              </div>
              <div class="info-item">
                <span class="label">邮箱：</span>
                <span class="value">{{ userInfo.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">角色：</span>
                <el-tag :type="getRoleType(userInfo.role)">
                  {{ getRoleText(userInfo.role) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatTime(userInfo.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后登录：</span>
                <span class="value">{{ formatTime(userInfo.last_login) }}</span>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 登录历史 -->
        <el-card class="login-history-card">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>最近登录</span>
            </div>
          </template>
          
          <el-timeline>
            <el-timeline-item 
              v-for="login in loginHistory" 
              :key="login.id"
              :timestamp="formatTime(login.login_time)"
              placement="top"
            >
              <div class="login-item">
                <div class="login-ip">
                  <el-icon><Monitor /></el-icon>
                  {{ login.ip_address }}
                </div>
                <div class="login-location">{{ login.location }}</div>
                <div class="login-device">{{ login.device }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      
      <!-- 设置面板 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>账户设置</span>
            </div>
          </template>
          
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form 
                :model="basicForm" 
                :rules="basicRules" 
                ref="basicFormRef" 
                label-width="100px"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input 
                    v-model="basicForm.username" 
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" placeholder="请输入邮箱地址" />
                </el-form-item>
                
                <el-form-item label="真实姓名" prop="real_name">
                  <el-input v-model="basicForm.real_name" placeholder="请输入真实姓名" />
                </el-form-item>
                
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="basicForm.phone" placeholder="请输入手机号码" />
                </el-form-item>
                
                <el-form-item label="部门" prop="department">
                  <el-input v-model="basicForm.department" placeholder="请输入所属部门" />
                </el-form-item>
                
                <el-form-item label="个人简介" prop="bio">
                  <el-input 
                    v-model="basicForm.bio" 
                    type="textarea" 
                    :rows="4"
                    placeholder="请输入个人简介"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo" :loading="isUpdating">
                    保存修改
                  </el-button>
                  <el-button @click="resetBasicForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            
            <!-- 安全设置 -->
            <el-tab-pane label="安全设置" name="security">
              <div class="security-section">
                <h4>密码设置</h4>
                <el-form 
                  :model="passwordForm" 
                  :rules="passwordRules" 
                  ref="passwordFormRef" 
                  label-width="120px"
                >
                  <el-form-item label="当前密码" prop="current_password">
                    <el-input 
                      v-model="passwordForm.current_password" 
                      type="password" 
                      show-password
                      placeholder="请输入当前密码"
                    />
                  </el-form-item>
                  
                  <el-form-item label="新密码" prop="new_password">
                    <el-input 
                      v-model="passwordForm.new_password" 
                      type="password" 
                      show-password
                      placeholder="请输入新密码"
                    />
                  </el-form-item>
                  
                  <el-form-item label="确认新密码" prop="confirm_password">
                    <el-input 
                      v-model="passwordForm.confirm_password" 
                      type="password" 
                      show-password
                      placeholder="请再次输入新密码"
                    />
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button type="primary" @click="updatePassword" :loading="isUpdatingPassword">
                      修改密码
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
              
              <el-divider />
              
              <div class="security-section">
                <h4>安全操作</h4>
                <div class="security-actions">
                  <div class="security-item">
                    <div class="security-info">
                      <h5>注销所有会话</h5>
                      <p>强制退出所有设备上的登录状态</p>
                    </div>
                    <el-button type="warning" @click="logoutAllSessions">
                      注销所有会话
                    </el-button>
                  </div>
                  
                  <div class="security-item">
                    <div class="security-info">
                      <h5>启用两步验证</h5>
                      <p>提高账户安全性，防止未授权访问</p>
                    </div>
                    <el-switch 
                      v-model="twoFactorEnabled" 
                      @change="toggleTwoFactor"
                      :loading="isTwoFactorLoading"
                    />
                  </div>
                  
                  <div class="security-item">
                    <div class="security-info">
                      <h5>API密钥管理</h5>
                      <p>管理用于API访问的密钥</p>
                    </div>
                    <el-button @click="showApiKeyDialog = true">
                      管理API密钥
                    </el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <!-- 通知设置 -->
            <el-tab-pane label="通知设置" name="notifications">
              <div class="notification-section">
                <h4>邮件通知</h4>
                <div class="notification-item">
                  <el-checkbox v-model="notificationSettings.email_login">
                    登录通知
                  </el-checkbox>
                  <span class="notification-desc">当账户在新设备上登录时发送邮件通知</span>
                </div>
                
                <div class="notification-item">
                  <el-checkbox v-model="notificationSettings.email_extraction">
                    提取通知
                  </el-checkbox>
                  <span class="notification-desc">当有新的文章提取请求时发送邮件通知</span>
                </div>
                
                <div class="notification-item">
                  <el-checkbox v-model="notificationSettings.email_system">
                    系统通知
                  </el-checkbox>
                  <span class="notification-desc">接收系统维护、更新等重要通知</span>
                </div>
                
                <el-divider />
                
                <h4>系统通知</h4>
                <div class="notification-item">
                  <el-checkbox v-model="notificationSettings.browser_notifications">
                    浏览器通知
                  </el-checkbox>
                  <span class="notification-desc">允许显示浏览器桌面通知</span>
                </div>
                
                <div class="notification-item">
                  <el-checkbox v-model="notificationSettings.sound_notifications">
                    声音提醒
                  </el-checkbox>
                  <span class="notification-desc">在重要操作完成时播放提示音</span>
                </div>
                
                <el-form-item style="margin-top: 2rem;">
                  <el-button type="primary" @click="updateNotificationSettings" :loading="isUpdatingNotifications">
                    保存设置
                  </el-button>
                </el-form-item>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
      <div class="avatar-upload">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="uploadAvatar"
        >
          <img v-if="previewAvatar" :src="previewAvatar" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>请选择图片文件</p>
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAvatarDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAvatar" :loading="isUploadingAvatar">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- API密钥管理对话框 -->
    <el-dialog v-model="showApiKeyDialog" title="API密钥管理" width="600px">
      <div class="api-key-section">
        <div class="api-key-header">
          <el-button type="primary" @click="generateApiKey" :loading="isGeneratingKey">
            <el-icon><Key /></el-icon>
            生成新密钥
          </el-button>
        </div>
        
        <el-table :data="apiKeys" style="width: 100%">
          <el-table-column prop="name" label="密钥名称" />
          <el-table-column prop="key" label="密钥值">
            <template #default="scope">
              <span class="api-key-value">{{ maskApiKey(scope.row.key) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间">
            <template #default="scope">
              {{ formatTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="last_used" label="最后使用">
            <template #default="scope">
              {{ scope.row.last_used ? formatTime(scope.row.last_used) : '从未使用' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button text type="danger" @click="deleteApiKey(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  UserFilled, Setting, Clock, Monitor, Plus, Key
} from '@element-plus/icons-vue'

// 用户信息
const userInfo = reactive({
  username: 'admin',
  email: '<EMAIL>',
  role: 'super_admin',
  created_at: '2024-01-01T00:00:00',
  last_login: '2024-01-15T10:30:00',
  avatar: ''
})

// 登录历史
const loginHistory = ref([
  {
    id: 1,
    login_time: '2024-01-15T10:30:00',
    ip_address: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10'
  },
  {
    id: 2,
    login_time: '2024-01-14T16:45:00',
    ip_address: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10'
  },
  {
    id: 3,
    login_time: '2024-01-13T09:20:00',
    ip_address: '*************',
    location: '上海市',
    device: 'Safari 17.0 / macOS'
  }
])

// API密钥列表
const apiKeys = ref([
  {
    id: 1,
    name: '开发环境密钥',
    key: 'ak_dev_abc123def456ghi789jkl012mno345',
    created_at: '2024-01-10T10:00:00',
    last_used: '2024-01-15T08:30:00'
  },
  {
    id: 2,
    name: '生产环境密钥',
    key: 'ak_prod_xyz789uvw456rst123opq890lmn567',
    created_at: '2024-01-05T14:20:00',
    last_used: null
  }
])

const activeTab = ref('basic')
const isUpdating = ref(false)
const isUpdatingPassword = ref(false)
const isUpdatingNotifications = ref(false)
const isUploadingAvatar = ref(false)
const isGeneratingKey = ref(false)
const isTwoFactorLoading = ref(false)
const showAvatarDialog = ref(false)
const showApiKeyDialog = ref(false)
const previewAvatar = ref('')
const twoFactorEnabled = ref(false)

// 基本信息表单
const basicForm = reactive({
  username: '',
  email: '',
  real_name: '',
  phone: '',
  department: '',
  bio: ''
})

const basicFormRef = ref<FormInstance>()

const basicRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ]
}

// 密码表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

const passwordFormRef = ref<FormInstance>()

const passwordRules: FormRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 通知设置
const notificationSettings = reactive({
  email_login: true,
  email_extraction: true,
  email_system: true,
  browser_notifications: false,
  sound_notifications: true
})

// 角色映射
const roleMap = {
  super_admin: { text: '超级管理员', type: 'danger' },
  admin: { text: '管理员', type: 'warning' },
  operator: { text: '运营人员', type: 'primary' },
  user: { text: '普通用户', type: 'info' }
}

// 获取角色文本
const getRoleText = (role: string) => {
  return roleMap[role as keyof typeof roleMap]?.text || '未知'
}

// 获取角色类型
const getRoleType = (role: string) => {
  return roleMap[role as keyof typeof roleMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 隐藏API密钥
const maskApiKey = (key: string) => {
  if (key.length <= 8) return key
  return key.substring(0, 8) + '...' + key.substring(key.length - 8)
}

// 更新基本信息
const updateBasicInfo = async () => {
  if (!basicFormRef.value) return
  
  try {
    const valid = await basicFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isUpdating.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新用户信息
    Object.assign(userInfo, basicForm)
    
    ElMessage.success('基本信息更新成功')
  } catch (error) {
    console.error('Update basic info failed:', error)
    ElMessage.error('更新失败')
  } finally {
    isUpdating.value = false
  }
}

// 重置基本信息表单
const resetBasicForm = () => {
  Object.assign(basicForm, {
    username: userInfo.username,
    email: userInfo.email,
    real_name: '',
    phone: '',
    department: '',
    bio: ''
  })
}

// 更新密码
const updatePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isUpdatingPassword.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('密码修改成功')
    
    // 重置表单
    passwordFormRef.value?.resetFields()
  } catch (error) {
    console.error('Update password failed:', error)
    ElMessage.error('密码修改失败')
  } finally {
    isUpdatingPassword.value = false
  }
}

// 更新通知设置
const updateNotificationSettings = async () => {
  isUpdatingNotifications.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('通知设置已保存')
  } catch (error) {
    console.error('Update notification settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isUpdatingNotifications.value = false
  }
}

// 注销所有会话
const logoutAllSessions = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要注销所有设备上的登录会话吗？您需要重新登录。',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('所有会话已注销')
  } catch {
    // 用户取消
  }
}

// 切换两步验证
const toggleTwoFactor = async (enabled: boolean) => {
  isTwoFactorLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`两步验证已${enabled ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('Toggle two factor failed:', error)
    ElMessage.error('操作失败')
    twoFactorEnabled.value = !enabled // 回滚
  } finally {
    isTwoFactorLoading.value = false
  }
}

// 头像上传前检查
const beforeAvatarUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
    return false
  }
  
  // 预览
  const reader = new FileReader()
  reader.onload = (e) => {
    previewAvatar.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
  
  return false // 阻止自动上传
}

// 上传头像
const uploadAvatar = async () => {
  // 这里应该实现实际的上传逻辑
  return { success: true }
}

// 保存头像
const saveAvatar = async () => {
  if (!previewAvatar.value) {
    ElMessage.warning('请先选择头像图片')
    return
  }
  
  isUploadingAvatar.value = true
  
  try {
    // 模拟上传
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    userInfo.avatar = previewAvatar.value
    showAvatarDialog.value = false
    previewAvatar.value = ''
    
    ElMessage.success('头像更新成功')
  } catch (error) {
    console.error('Upload avatar failed:', error)
    ElMessage.error('头像上传失败')
  } finally {
    isUploadingAvatar.value = false
  }
}

// 生成API密钥
const generateApiKey = async () => {
  isGeneratingKey.value = true
  
  try {
    // 模拟生成
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newKey = {
      id: Date.now(),
      name: `密钥_${Date.now()}`,
      key: `ak_${Math.random().toString(36).substr(2, 32)}`,
      created_at: new Date().toISOString(),
      last_used: null
    }
    
    apiKeys.value.unshift(newKey)
    ElMessage.success('API密钥生成成功')
  } catch (error) {
    console.error('Generate API key failed:', error)
    ElMessage.error('生成失败')
  } finally {
    isGeneratingKey.value = false
  }
}

// 删除API密钥
const deleteApiKey = async (key: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个API密钥吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    const index = apiKeys.value.findIndex(k => k.id === key.id)
    if (index > -1) {
      apiKeys.value.splice(index, 1)
      ElMessage.success('API密钥已删除')
    }
  } catch {
    // 用户取消
  }
}

// 页面初始化
onMounted(() => {
  // 初始化基本信息表单
  resetBasicForm()
})
</script>

<style scoped>
.profile-center {
  height: 100%;
}

.profile-card {
  margin-bottom: 1.5rem;
}

.login-history-card {
  height: 400px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.profile-info {
  text-align: center;
}

.avatar-section {
  margin-bottom: 2rem;
}

.info-section {
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
}

.login-item {
  padding: 0.5rem;
  background-color: #fafafa;
  border-radius: 4px;
}

.login-ip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.login-location,
.login-device {
  font-size: 14px;
  color: #909399;
}

.security-section {
  margin-bottom: 2rem;
}

.security-section h4 {
  margin-bottom: 1rem;
  color: #303133;
}

.security-actions {
  space-y: 1rem;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.security-info h5 {
  margin: 0 0 0.5rem 0;
  color: #303133;
}

.security-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.notification-section {
  space-y: 1rem;
}

.notification-section h4 {
  margin-bottom: 1rem;
  color: #303133;
}

.notification-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.notification-desc {
  margin-left: 1rem;
  color: #909399;
  font-size: 14px;
}

.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
}

.upload-tips {
  margin-top: 1rem;
}

.upload-tips p {
  margin: 0.25rem 0;
  color: #909399;
  font-size: 14px;
}

.api-key-section {
  margin-top: 1rem;
}

.api-key-header {
  margin-bottom: 1rem;
}

.api-key-value {
  font-family: 'Courier New', monospace;
  color: #606266;
}

@media (max-width: 768px) {
  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .notification-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .notification-desc {
    margin-left: 0;
  }
}
</style>
