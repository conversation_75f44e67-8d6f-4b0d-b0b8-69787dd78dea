{"name": "csdn-vip-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.7.7", "element-plus": "^2.8.8", "pinia": "^2.2.6", "vue": "^3.5.18", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^0.18.4", "unplugin-vue-components": "^0.27.4", "vite": "^7.1.0", "vue-tsc": "^3.0.5"}}