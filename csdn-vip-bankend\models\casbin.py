from .base import BaseModel, fields


class AdminRole(BaseModel):
    """Admin角色表"""
    name = fields.CharField(max_length=256, description='名称')
    role = fields.CharField(max_length=256, unique=True, description='角色')
    role_admin = fields.ForeignKeyField(
        "template.AdminUser",  # 这是用于将AdminRole表连接到AdminUser表，template.AdminUser是目标模型的完整路径
        null=True,  # 这里指定外键能为空
        on_delete=fields.CASCADE,
        # 这是一个选项，它定义了如何在目标模型中处理外键相关的删除操作。在这里，我们将其设置为Cascade，意味着当与此外键相关联的Projects记录被删除时，与之相关的ProjectResource记录也将被级联删除。
        related_name="manage_roles",
        description="关联到用户名"
    )
    parent_role = fields.ForeignKeyField('template.AdminRole', default=None, null=True, related_name='children_role',
                                         description="父角色")
    children_role: fields.ReverseRelation["AdminRole"]

    class Meta:
        table = 'admin_role'

    class PydanticMeta:
        exclude = ['is_deleted']


class AdminObject(BaseModel):
    """Admin Casbin目标(访问资源)表"""
    name = fields.CharField(max_length=256, description='名称')
    obj = fields.CharField(max_length=256, description='目标(访问资源)')
    acts: fields.ManyToManyRelation["AdminAction"] = fields.ManyToManyField(
        "template.AdminAction", related_name="objs", through="admin_casbin_object_action"
    )
    type = fields.CharField(max_length=256, default="api", null=True, description='资源类型')

    class Meta:
        table = 'admin_casbin_object'

    class PydanticMeta:
        exclude = ['is_deleted']


class AdminAction(BaseModel):
    """Admin Casbin动作(访问方式)表"""
    name = fields.CharField(max_length=256, description='名称')
    act = fields.CharField(max_length=256, description='动作(访问方式)')
    objs: fields.ManyToManyRelation[AdminObject]

    class Meta:
        table = 'admin_casbin_action'

    class PydanticMeta:
        exclude = ['is_deleted']
