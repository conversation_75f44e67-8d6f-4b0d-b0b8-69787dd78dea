from typing import Dict
from fastapi import APIRouter, Depends
from core.response import ResultResponse
from core.sdk.tencent_cloud import get_cos, get_sts
from config import settings
from schemas import CosUrlQuery

cos_token_router = APIRouter(
    prefix="/cos",
    tags=["COS临时访问授权"],
)

@cos_token_router.get(
    "-token",
    summary='Obtain COS temporary token',
    name='Obtain COS temporary token',
    response_model=ResultResponse
)
async def get_cos_token():
    sts = get_sts()
    response = sts.get_credential()
    return ResultResponse[Dict](result=dict(response))


@cos_token_router.get(
    "-url",
    summary='Obtain COS temporary url',
    name='Obtain COS temporary url',
    response_model=ResultResponse
)
async def get_cos_url(query_params: CosUrlQuery = Depends()):
    client = get_cos()
    url = client.get_presigned_url(
        Method='GET',
        Bucket=settings.TENCENT.COS.BUCKET,
        Key=query_params.key,
        Expired=60 * 60  # 过期时间请根据自身场景定义
    )
    return ResultResponse[str](result=url)
