from .base import BaseModel, fields
from enum import IntEnum
from datetime import datetime


class TokenType(IntEnum):
    """Token类型"""
    TIME_LIMITED = 1  # 时间限制类型（但仍有次数限制防刷量）
    COUNT_LIMITED = 2  # 次数限制类型（不限制时间）


class TokenStatus(IntEnum):
    """Token状态"""
    ACTIVE = 1      # 激活状态
    EXPIRED = 2     # 已过期
    EXHAUSTED = 3   # 次数用完
    DISABLED = 4    # 已禁用


class AccessToken(BaseModel):
    """访问令牌表"""
    token = fields.CharField(max_length=128, unique=True, null=False, description="访问令牌")
    name = fields.CharField(max_length=128, null=True, description="令牌名称（备注）")
    token_type: TokenType = fields.IntEnumField(TokenType, description="令牌类型")
    status: TokenStatus = fields.IntEnumField(TokenStatus, default=TokenStatus.ACTIVE, description="令牌状态")
    
    # 权限控制字段
    max_usage_count = fields.IntField(null=True, description="最大使用次数")
    used_count = fields.IntField(default=0, description="已使用次数")
    expire_time = fields.DatetimeField(null=True, description="过期时间")
    
    # 限流控制字段
    max_daily_usage = fields.IntField(default=100, description="每日最大使用次数")
    max_hourly_usage = fields.IntField(default=10, description="每小时最大使用次数")
    
    # 创建者信息
    created_by = fields.ForeignKeyField('template.AdminUser', related_name='created_tokens', null=True, description="创建者")
    
    # 最后使用时间
    last_used_time = fields.DatetimeField(null=True, description="最后使用时间")
    
    # 其他字段
    client_ip = fields.CharField(max_length=45, null=True, description="绑定的客户端IP（可选）")
    notes = fields.TextField(null=True, description="备注信息")

    class Meta:
        table = "access_token"
        
    class PydanticMeta:
        exclude = ['is_deleted']

    def is_valid(self) -> bool:
        """检查token是否有效"""
        if self.status != TokenStatus.ACTIVE:
            return False
            
        # 检查过期时间
        if self.expire_time and datetime.now() > self.expire_time:
            return False
            
        # 检查使用次数
        if self.max_usage_count and self.used_count >= self.max_usage_count:
            return False
            
        return True

    async def increment_usage(self):
        """增加使用次数"""
        self.used_count += 1
        self.last_used_time = datetime.now()
        
        # 更新状态
        if self.max_usage_count and self.used_count >= self.max_usage_count:
            self.status = TokenStatus.EXHAUSTED
        elif self.expire_time and datetime.now() > self.expire_time:
            self.status = TokenStatus.EXPIRED
            
        await self.save()
