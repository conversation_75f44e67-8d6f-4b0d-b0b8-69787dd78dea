from typing import Generic, TypeVar, Optional, List
from pydantic import Field
from pydantic.generics import GenericModel

Data = TypeVar('Data')


class ResultResponseError(Exception):
    def __init__(self, code, result):
        super().__init__(result)
        self.code = code
        self.result = result


class ResultResponse(GenericModel, Generic[Data]):
    code: int = Field(default=200, description='返回码')
    message: str = Field(default='请求成功', description='消息内容')
    result: Optional[Data]


class ListResult(GenericModel, Generic[Data]):
    all_count = Field(default=0, description='总数')
    data: Optional[List[Data]]
