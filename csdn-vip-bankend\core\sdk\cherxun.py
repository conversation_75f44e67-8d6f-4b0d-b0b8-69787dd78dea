import time
import json
import base64
import hashlib
from config import settings
from typing import Dict, Optional
from aiohttp import ClientSession
from .base import Base<PERSON>pi
from utils.customize_crypto import AES128CBCCrypto


class CheXunApi(BaseApi):
    def __init__(self, channel_no: str, secret_value: str, domain: str):
        self.channelNo = channel_no
        self.secretValue = secret_value
        self.domain = domain

    def _get_sign(self, data: Dict) -> str:
        # 排序后的字符串
        unsigned_items = self._ordered_data(data)
        message = "&".join(u"{}={}".format(k, v) for k, v in unsigned_items)
        md5_message = hashlib.md5(
            base64.b64encode(message.encode('utf-8')) + f'#{self.secretValue}'.encode('utf-8')).hexdigest()
        return md5_message.upper()

    def _encrypt(self, user_data: str) -> str:
        key = self.secretValue[:16]
        iv = self._generate_random_str()
        cryptor = AES128CBCCrypto(key.encode('utf-8'), iv.encode('utf-8'))
        row_data = {
            'v': user_data
        }
        pack_data = {
            'iv': base64.b64encode(iv.encode('utf-8')).decode(),
            'value': cryptor.encrypt(json.dumps(row_data))
        }
        return base64.b64encode(json.dumps(pack_data).encode('utf-8')).decode()

    def _add_signature(self, data: Dict) -> Dict:
        data['timestamp'] = int(time.time() * 1000)
        data['channelNo'] = self.channelNo
        data['sign'] = self._get_sign(data)
        return data

    async def get_user_open_id(self, phone: str, nick_name: str = None) -> Optional[str]:
        """
        获取用户的openid
        @param phone: 手机号		 必填
        @param nick_name: 昵称	  非必填,如果传了该字段,将会更新用户昵称
        @return: {
                    state: 0,
                    openId:"",
                    msg: "ok", //或者错误信息
                }
        """
        user_data = self._ignore_none(phone=phone, nickName=nick_name)
        data = {
            'data': self._encrypt(json.dumps(user_data))
        }
        data = self._add_signature(data)
        async with ClientSession() as session:
            async with session.post(self.domain + '/api/v1/open/auth', data=data) as response:
                data = json.loads(await response.text())
                if data['state'] == 0:
                    return data['openId']
                else:
                    return None

    async def create_product_order(
            self, open_id: str, product_id: str, duration: str, sub_channel: str = None) -> bool:
        """
        创建权益商品订单
        @param open_id: 用户凭证,如果存在多个会员存在续费需求,openId可以用","逗号分隔,最多支持同时 1W个会员同时续费
        @param product_id: 会员商品id,由澈讯提供
        @param duration: 时长,单位都是天 默认按照一天24小时计算
        @param sub_channel: 子渠道（可选）
        @return:{
                    state: 0
                    msg: "ok", //或者错误信息
                }
        """
        data = self._ignore_none(openId=open_id, productId=product_id, duration=duration, subChannel=sub_channel)
        data = self._add_signature(data)
        async with ClientSession() as session:
            async with session.post(self.domain + '/api/v1/open/exchange/member', data=data) as response:
                data = json.loads(await response.text())
                if data['state'] == 0:
                    return True
                else:
                    return False

    async def renew_product_order(
            self, open_id: str, product_id: str, duration: str, sub_channel: str = None) -> bool:
        """
        续费权益商品订单
        @param open_id: 用户凭证,如果存在多个会员存在续费需求,openId可以用","逗号分隔,最多支持同时 1W个会员同时续费
        @param product_id: 会员商品id,由澈讯提供
        @param duration: 时长,单位都是天 默认按照一天24小时计算
        @param sub_channel: 子渠道（可选）
        @return:{
                    state: 0
                    msg: "ok", //或者错误信息
                }
        """
        data = self._ignore_none(openId=open_id, productId=product_id, duration=duration, subChannel=sub_channel)
        data = self._add_signature(data)
        async with ClientSession() as session:
            async with session.post(self.domain + '/api/v1/open/member/renew', data=data) as response:
                data = json.loads(await response.text())
                if data['state'] == 0:
                    return True
                else:
                    return False


def get_chexun():
    return CheXunApi(
        channel_no=settings.CHEXUN.CHANNER_NO,
        secret_value=settings.CHEXUN.SECRET_VALUE,
        domain=settings.CHEXUN.DOMAIN
    )
