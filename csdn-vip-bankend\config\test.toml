[test]
APP.DEBUG = true
APP.TITLE = "product-template-backend"
APP.DESCRIPTION = "PDP"
APP.DOCS_URL = "/openapi/docs" # 文档地址 默认为docs
APP.OPENAPI_URL = "/openapi/openapi.json" # 文档关联请求数据接口
APP.REDOC_URL = "/openapi/redoc" # redoc 文档
APP.API_VERSION = "/api/v1" # API版本
APP.LOG_PATH = "./log" # 日志路径

#Sentry-DSN
SENTRY.DSN = "https://<EMAIL>/5"
SENTRY.ENVIRONMENT = "test"

# 数据库
POSTGRESQL.USER = "postgres"
POSTGRESQL.PASSWORD = "930901yhq"
POSTGRESQL.HOST = "localhost"
POSTGRESQL.PORT = 5432
POSTGRESQL.DATABASE = "product-template"

# JWT鉴权
JWT.ALGORITHM = "HS256"
JWT.SECRET_KEY = "aeq)s(*&(&)()WEQasd8**&^9asda_asdasd*&*&^+_sda11"
JWT.ACCESS_TOKEN_EXPIRE_MINUTES = 10080 # token过期时间 分钟

# 腾讯COS
TENCENT.COS.SECRET_ID = "AKIDjIfpY3PYUDi04wc4KMAk8NOAiZLuGQM5"
TENCENT.COS.SECRET_KEY = "aw75rPksoX2KVUFoSvAJH3bu0ua2GI5e"
TENCENT.COS.BUCKET = "union-pay-mall-1314564919"
TENCENT.COS.REGION = "ap-shanghai"

# 澈讯
CHEXUN.CHANNER_NO = "176fdf6b54c7f9c848e58eebdd2aee5a"
CHEXUN.SECRET_VALUE = "BD005AD1E536D40CEE87E442D4B4235F"
CHEXUN.DOMAIN = "https://testrights.chexun.link"