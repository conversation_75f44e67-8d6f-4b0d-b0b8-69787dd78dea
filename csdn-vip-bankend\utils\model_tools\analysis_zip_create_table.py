# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/5/31 21:50
# @File     : analysis_zip_create_table.py
# @Project  : items
import os
import zipfile

import chardet
import pandas as pd
import psycopg2
from sqlalchemy import create_engine, inspect
import urllib.request
import io

from config import settings

"""
{
    "table_name": "",
    "columns": [{"id": "int"}, {"name": "CHAR(50)"}, {"price": "numeric"}, {"description": "text"}, {"create_time": "timestamp"},
                {"update_time": "timestamp"}],
    "data": [{"id": 1, "name": "kunkun", "price": 3.28, "description": "阿喀琉斯结婚登记咖啡拉花时代和法律框架", "create_time": now_time, "update_time": now_time}, {"id": 2, "name": "kunkun2", "price": 3.282, "description": "阿喀琉斯结婚登记咖啡拉花时代和法律框架", "create_time": now_time, "update_time": now_time},
             {"id": 3, "name": "kunkun3", "price": 3.33333328, "description": "阿喀琉斯结婚登记咖啡拉花时代和法律框架", "create_time": now_time, "update_time": now_time}, {"id": 4, "name": "kunkun4", "price": 3.28, "description": "阿喀琉斯结婚登记咖啡拉花时代和法律框架", "create_time": now_time, "update_time": now_time}]
}
"""

engine = create_engine(f"postgresql+psycopg2://"
                       f"{settings.POSTGRESQL2.USER}:"
                       f"{settings.POSTGRESQL2.PASSWORD}@"
                       f"{settings.POSTGRESQL2.HOST}:"
                       f"{settings.POSTGRESQL2.PORT}/"
                       f"{settings.POSTGRESQL3.SAVE_DATABASE}")


async def save_csv_to_dataset(path, table_name):
    try:
        df = pd.read_csv(path, encoding='utf-8')
        print(1)
    except:
        try:
            df = pd.read_csv(path, encoding='gbk')
            print(2)
        except:
            try:
                df = pd.read_csv(path, encoding='gb18030')
                print(111)
            except:
                try:
                    df = pd.read_csv(path, encoding='gb2312')
                    print(111)
                except:
                    try:
                        df = pd.read_csv(path, encoding='ascii')
                        print(111)
                    except:
                        try:
                            df = pd.read_csv(path, encoding='ansi')
                            print(111)
                        except:
                            try:
                                df = pd.read_csv(path, encoding='utf-8-sig')
                                print(111)
                            except:
                                raise "文件格式不支持，请重新上传"
    # df = pd.read_csv(path)
    df.to_sql(table_name, engine, if_exists='replace', index=False)
    return table_name


async def get_schema_names():
    insp = inspect(engine)
    schema_names = insp.get_schema_names()
    return schema_names


async def get_table_names(schema='public'):
    insp = inspect(engine)
    table_names = insp.get_table_names(schema=schema)
    return table_names


async def get_columns(table_name, schema='public'):
    insp = inspect(engine)
    columns = insp.get_columns(table_name, schema=schema)  # 表名，库名
    return columns


async def read_zip(url: str, is_base: str) -> list:
    response = urllib.request.urlopen(url)
    data = response.read()

    # 将字节流对象包装成内存中的二进制文件流
    zip_file = io.BytesIO(data)
    zip_name = zip_file
    tables_name = []
    tables_columns = []
    with zipfile.ZipFile(zip_name, 'r') as zip:
        # 获取zip文件中的所有文件名
        files_name = zip.namelist()
        for filename in files_name:
            try:
                filename_cn = filename.encode('cp437').decode('utf-8')
                print(1)
            except:
                try:
                    filename_cn = filename.encode('cp437').decode('gbk')
                    print(2)
                except:
                    try:
                        filename_cn = filename.encode('cp437').decode('gb18030')
                        print(111)
                    except:
                        try:
                            filename_cn = filename.encode('cp437').decode('gb2312')
                            print(111)
                        except:
                            try:
                                filename_cn = filename.encode('cp437').decode('ascii')
                                print(111)
                            except:
                                try:
                                    filename_cn = filename.encode('cp437').decode('ansi')
                                    print(111)
                                except:
                                    try:
                                        filename_cn = filename.encode('cp437').decode('utf-8-sig')
                                        print(111)
                                    except:
                                        filename_cn = filename
            print(filename_cn)
            table_name = filename_cn.split(".")[0]
            table_name = f"{table_name}_{is_base}"
            # columns = []
            with zip.open(filename) as f:
                a = await save_csv_to_dataset(f, table_name)
                print(a)
            columns = await get_columns(table_name)
            print(columns)
            tables_name.append(table_name)
            tables_columns.append(columns)
    return [tables_name, tables_columns]


async def table2csv(table_name: str, database_name: str):
    schema = 'public'
    engine1 = create_engine(f"postgresql+psycopg2://"
                            f"{settings.POSTGRESQL2.USER}:"
                            f"{settings.POSTGRESQL2.PASSWORD}@"
                            f"{settings.POSTGRESQL2.HOST}:"
                            f"{settings.POSTGRESQL2.PORT}/"
                            f"{database_name}")
    insp = inspect(engine1)
    columns = insp.get_columns(table_name, schema=schema)  # 表名，库名
    first_path = "./table_data/"
    # 检测文件夹是否存在，如果不存在则创建
    if not os.path.exists(first_path):
        os.makedirs(first_path)
    columns_name = columns
    print(columns_name)
    # 限制读取100行
    sql = f"SELECT * FROM {schema}.{table_name} limit 100"
    print(sql)

    df = pd.read_sql(sql, engine1)

    # 检测是否有mydata.csv文件，如果有则删除
    if os.path.exists(f'{first_path}{table_name}.csv'):
        os.remove(f'{first_path}{table_name}.csv')

    # 将数据保存到 CSV 文件中
    df.to_csv(f'{first_path}{table_name}.csv', index=False, encoding='utf-8')

    # 检测是否有mydata.zip文件，如果有则删除
    if os.path.exists(f'{first_path}{table_name}.zip'):
        os.remove(f'{first_path}{table_name}.zip')

    # 将 CSV 文件压缩为 ZIP 文件
    with zipfile.ZipFile(f'{first_path}{table_name}.zip', 'w') as z:
        z.write(f'{first_path}{table_name}.csv')

    # 返回文件路径
    file_path = f'{first_path}{table_name}.zip'
    file_name = f'{table_name}.zip'

    return [file_path, file_name]

    # sleep(10)

    # # 删除生成的zip文件
    # os.remove(f'{first_path}{table_name}.csv')
    # os.remove(f'{first_path}{table_name}.zip')
