from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
import time
import asyncio
from typing import Dict, Optional
from collections import defaultdict, deque
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """基于内存的速率限制器"""
    
    def __init__(self):
        # 存储每个IP的请求记录
        self.ip_requests: Dict[str, deque] = defaultdict(lambda: deque())
        # 存储每个token的请求记录
        self.token_requests: Dict[str, deque] = defaultdict(lambda: deque())
        # 存储被阻止的IP
        self.blocked_ips: Dict[str, float] = {}
        # 清理任务运行状态
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_records())
    
    async def _cleanup_expired_records(self):
        """定期清理过期的记录"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                current_time = time.time()
                
                # 清理IP请求记录
                for ip, requests in list(self.ip_requests.items()):
                    # 清理1小时前的记录
                    while requests and current_time - requests[0] > 3600:
                        requests.popleft()
                    if not requests:
                        del self.ip_requests[ip]
                
                # 清理token请求记录
                for token, requests in list(self.token_requests.items()):
                    # 清理1小时前的记录
                    while requests and current_time - requests[0] > 3600:
                        requests.popleft()
                    if not requests:
                        del self.token_requests[token]
                
                # 清理过期的IP阻止记录
                for ip in list(self.blocked_ips.keys()):
                    if current_time - self.blocked_ips[ip] > 3600:  # 阻止1小时
                        del self.blocked_ips[ip]
                        
            except Exception as e:
                logger.error(f"Rate limiter cleanup error: {e}")
    
    def is_ip_blocked(self, ip: str) -> bool:
        """检查IP是否被阻止"""
        if ip in self.blocked_ips:
            # 检查阻止是否已过期
            if time.time() - self.blocked_ips[ip] > 3600:  # 阻止1小时
                del self.blocked_ips[ip]
                return False
            return True
        return False
    
    def block_ip(self, ip: str):
        """阻止IP"""
        self.blocked_ips[ip] = time.time()
        logger.warning(f"Blocked IP: {ip}")
    
    def check_rate_limit(
        self, 
        ip: str, 
        token: Optional[str] = None,
        max_requests_per_minute: int = 60,
        max_requests_per_hour: int = 1000,
        max_token_requests_per_hour: int = 100
    ) -> tuple[bool, str]:
        """
        检查速率限制
        返回 (是否允许, 错误消息)
        """
        current_time = time.time()
        
        # 检查IP是否被阻止
        if self.is_ip_blocked(ip):
            return False, "IP已被临时阻止，请稍后再试"
        
        # 检查IP级别的限制
        ip_requests = self.ip_requests[ip]
        
        # 清理过期的请求记录
        minute_ago = current_time - 60
        hour_ago = current_time - 3600
        
        # 清理1分钟前的记录
        while ip_requests and ip_requests[0] < minute_ago:
            ip_requests.popleft()
        
        # 检查每分钟限制
        minute_requests = sum(1 for req_time in ip_requests if req_time >= minute_ago)
        if minute_requests >= max_requests_per_minute:
            # 如果超过每分钟限制，暂时阻止该IP
            if minute_requests >= max_requests_per_minute * 2:
                self.block_ip(ip)
            return False, f"每分钟请求次数超限，最多 {max_requests_per_minute} 次"
        
        # 检查每小时限制
        hour_requests = sum(1 for req_time in ip_requests if req_time >= hour_ago)
        if hour_requests >= max_requests_per_hour:
            return False, f"每小时请求次数超限，最多 {max_requests_per_hour} 次"
        
        # 检查token级别的限制（如果提供了token）
        if token:
            token_requests = self.token_requests[token]
            
            # 清理token的过期记录
            while token_requests and token_requests[0] < hour_ago:
                token_requests.popleft()
            
            # 检查token每小时限制
            token_hour_requests = len(token_requests)
            if token_hour_requests >= max_token_requests_per_hour:
                return False, f"Token每小时请求次数超限，最多 {max_token_requests_per_hour} 次"
        
        return True, ""
    
    def record_request(self, ip: str, token: Optional[str] = None):
        """记录请求"""
        current_time = time.time()
        self.ip_requests[ip].append(current_time)
        
        if token:
            self.token_requests[token].append(current_time)


# 全局速率限制器实例
rate_limiter = RateLimiter()


class RateLimitMiddleware:
    """速率限制中间件"""
    
    def __init__(
        self,
        max_requests_per_minute: int = 60,
        max_requests_per_hour: int = 1000,
        max_token_requests_per_hour: int = 100
    ):
        self.max_requests_per_minute = max_requests_per_minute
        self.max_requests_per_hour = max_requests_per_hour
        self.max_token_requests_per_hour = max_token_requests_per_hour
    
    async def __call__(self, request: Request, call_next):
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        
        # 从请求中提取token（如果有）
        token = self._extract_token(request)
        
        # 检查速率限制
        allowed, error_message = rate_limiter.check_rate_limit(
            ip=client_ip,
            token=token,
            max_requests_per_minute=self.max_requests_per_minute,
            max_requests_per_hour=self.max_requests_per_hour,
            max_token_requests_per_hour=self.max_token_requests_per_hour
        )
        
        if not allowed:
            logger.warning(f"Rate limit exceeded for IP {client_ip}: {error_message}")
            return JSONResponse(
                status_code=429,
                content={
                    "success": False,
                    "message": error_message,
                    "error_code": "RATE_LIMIT_EXCEEDED"
                }
            )
        
        # 记录请求
        rate_limiter.record_request(client_ip, token)
        
        # 继续处理请求
        response = await call_next(request)
        
        # 添加速率限制头部信息
        response.headers["X-RateLimit-Limit"] = str(self.max_requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(
            max(0, self.max_requests_per_minute - len(rate_limiter.ip_requests[client_ip]))
        )
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 尝试从代理头部获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接连接IP
        return request.client.host if request.client else "unknown"
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取token"""
        try:
            # 从Authorization头部提取
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                return auth_header[7:]
            
            # 从查询参数提取
            token = request.query_params.get("token")
            if token:
                return token
            
            # 尝试从请求体中提取（对于POST请求）
            if hasattr(request, "_body"):
                import json
                try:
                    body = json.loads(request._body)
                    if isinstance(body, dict) and "token" in body:
                        return body["token"]
                except:
                    pass
            
        except Exception as e:
            logger.debug(f"Token extraction error: {e}")
        
        return None


def create_rate_limit_middleware(
    max_requests_per_minute: int = 60,
    max_requests_per_hour: int = 1000,
    max_token_requests_per_hour: int = 100
) -> RateLimitMiddleware:
    """创建速率限制中间件"""
    return RateLimitMiddleware(
        max_requests_per_minute=max_requests_per_minute,
        max_requests_per_hour=max_requests_per_hour,
        max_token_requests_per_hour=max_token_requests_per_hour
    )
