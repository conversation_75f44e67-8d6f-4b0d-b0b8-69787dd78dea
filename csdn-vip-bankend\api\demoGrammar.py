from models import (
    Student, Teacher, Books, BooksVersion, AdminUser
)
from schemas import (
    StudentUpdate, StudentCreateBase, StudentOut, \
    TeacherOut, BooksOut, BooksVersionOut, BooksAdd, BooksGet, StudentGet
)
from typing import cast
from utils.base import ignore_none
from fastapi import Depends
from utils.fastapi_crudrouter import TortoiseCR<PERSON><PERSON>outer
from core.response import ResultResponse, ListResult
from core.auth.base import get_current_active_user

student_router = TortoiseCRUDRouter(
    db_model=Student,
    list_schema=StudentOut,
    create_schema=StudentCreateBase,
    update_schema=StudentUpdate,
    prefix="/demo",
    tags=["测试"],
    get_all_route=[Depends(get_current_active_user)],
    get_one_route=[Depends(get_current_active_user)],
    create_route=[Depends(get_current_active_user)],
    update_route=[Depends(get_current_active_user)],
    delete_one_route=[Depends(get_current_active_user)],
)



@student_router.post(
    "-book_add",
    name="add book",
    summary="add book",
    response_model=ResultResponse[BooksOut],
    dependencies=[Depends(get_current_active_user)]
)
async def book_add(param: BooksAdd,
                   current_user: AdminUser = Depends(get_current_active_user)
                   ) -> ResultResponse[BooksOut]:
    info = param.dict()
    data = await Books.create(**info)
    orm = await BooksOut.from_tortoise_orm(data)
    return ResultResponse[BooksOut](result=orm)


@student_router.get(
    "-get_book_",
    name="Get books",
    summary="Get books",
    response_model=ResultResponse[BooksOut],
    dependencies=[Depends(get_current_active_user)]
)
async def get_admin_casbin_object(query_params: BooksGet = Depends()) -> \
        ResultResponse[BooksOut]:
    ids = query_params.ids
    query =await Books.filter(id=ids).first()
    aa = await Books.filter(id=ids).all()
    orm = await BooksOut.from_tortoise_orm(query)
    return ResultResponse[BooksOut](result=orm)


@student_router.get(
    "-get_student",
    name="Get student",
    summary="Get student",
    response_model=ResultResponse[ListResult[StudentOut]],
    dependencies=[Depends(get_current_active_user)]
)
async def get_students_info(query_params: StudentGet = Depends()) -> \
        ResultResponse[ListResult[StudentOut]]:
    skip, limit, ids = query_params.skip, query_params.limit, query_params.ids
    query = Student.filter(
        is_deleted=False,
        **ignore_none(id=ids),
    )
    all_count = await query.count()
    query = query.all().order_by('id').offset(cast(int, skip))
    if limit:
        query = query.limit(limit)
    data = await StudentOut.from_queryset(query)
    return ResultResponse[ListResult[StudentOut]](
        result=ListResult[StudentOut](
            all_count=all_count,
            data=data
        )
    )


