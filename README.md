# CSDN文章提取系统

一个高效、安全的CSDN文章内容提取中转系统，支持Token权限管理、智能去重、使用统计等功能。

## 🌟 系统特色

- **Token权限管理**: 支持时间限制和次数限制两种类型的访问令牌
- **智能去重**: 自动识别重复文章，避免重复提取，节省资源
- **负载均衡**: 支持多个提取核心，自动负载均衡
- **使用统计**: 详细的使用记录和统计分析
- **安全防护**: 完善的限流、防刷、权限验证机制
- **现代界面**: 基于Vue 3 + Element Plus的现代化前端界面

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   前端界面      │◄──►│   后端API      │◄──►│  提取核心服务    │
│  (Vue 3 + EP)   │    │  (FastAPI)      │    │  (外部服务)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │                 │
                       │   PostgreSQL    │
                       │     数据库      │
                       │                 │
                       └─────────────────┘
```

## 📋 功能特性

### 核心功能
- **Token管理**: 创建、更新、删除访问令牌
- **文章提取**: 单篇和批量文章提取
- **历史记录**: 查看提取历史和统计
- **去重机制**: 基于URL哈希的智能去重

### 权限控制
- **时间限制**: 设置令牌过期时间
- **次数限制**: 限制最大使用次数
- **频率限制**: 每小时/每日使用限制
- **IP绑定**: 可选的IP地址绑定

### 安全特性
- **限流保护**: 防止恶意刷接口
- **安全检测**: 检测恶意请求模式
- **访问日志**: 详细的访问和使用日志
- **权限验证**: 完善的身份验证机制

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- SQLite3 (Python内置，无需额外安装)

### 1. 克隆项目

```bash
git clone <repository-url>
cd csdn-vip
```

### 2. 后端设置

```bash
cd csdn-vip-bankend

# 安装依赖
pip install -r requirements.txt

# 无需配置数据库，SQLite会自动创建
# 如需自定义数据库路径，可编辑 config/dev.toml 文件

# 初始化系统（自动创建SQLite数据库）
python init_system.py

# 启动后端服务
python main.py
```

### 3. 前端设置

```bash
cd csdn-vip-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. 访问系统

- 前端用户界面: http://localhost:5173
- 后端API文档: http://localhost:11211/openapi/docs
- 管理后台: http://localhost:5173/admin

## 📖 使用指南

### 用户使用流程

1. **获取访问令牌**: 从管理员处获取访问令牌
2. **验证令牌**: 在首页输入令牌进行验证
3. **提取文章**: 输入CSDN文章URL进行提取
4. **查看历史**: 查看提取历史和统计信息

### 管理员操作

1. **登录管理后台**: 使用管理员账号登录
2. **创建访问令牌**: 根据需要创建不同类型的令牌
3. **配置提取核心**: 添加和配置提取核心服务
4. **监控系统状态**: 查看系统使用情况和性能指标

## ⚙️ 配置说明

### 数据库配置 (`config/dev.toml`)

```toml
# SQLite数据库配置（开箱即用）
[SQLITE]
DATABASE = "data/csdn_vip.db"  # 数据库文件路径
```

### JWT配置

```toml
[JWT]
ALGORITHM = "HS256"
SECRET_KEY = "your_secret_key"
ACCESS_TOKEN_EXPIRE_MINUTES = 10080
```

### 提取核心配置

在管理后台中配置提取核心服务的API端点和认证信息。

## 🔧 API接口

### 核心接口

- `POST /api/v1/access-token/validate` - 验证访问令牌
- `POST /api/v1/extraction/extract` - 提取单篇文章
- `POST /api/v1/extraction/batch-extract` - 批量提取文章
- `POST /api/v1/extraction/history` - 获取提取历史

### 管理接口

- `GET /api/v1/access-token` - 获取令牌列表
- `POST /api/v1/access-token` - 创建访问令牌
- `GET /api/v1/extraction-core` - 获取提取核心列表
- `POST /api/v1/extraction-core` - 添加提取核心

详细API文档请访问: http://localhost:11211/openapi/docs

## 🛡️ 安全特性

### 限流机制
- 每分钟最多60次请求
- 每小时最多1000次请求
- Token级别的使用限制

### 安全检测
- SQL注入检测
- XSS攻击检测
- 路径遍历检测
- 恶意User-Agent检测

### 访问控制
- IP白名单/黑名单
- Token权限验证
- 请求频率限制

## 📊 监控和统计

### 使用统计
- 总请求次数
- 成功率统计
- 响应时间分析
- 用户活跃度

### 系统监控
- 提取核心健康状态
- 负载均衡情况
- 错误率监控
- 性能指标

## 🔍 故障排除

### 常见问题

**1. 数据库连接失败**
- 检查SQLite数据库文件是否存在
- 确认data目录有读写权限
- 验证磁盘空间是否充足

**2. 提取失败**
- 检查提取核心配置
- 验证API端点是否可访问
- 查看错误日志

**3. 令牌验证失败**
- 确认令牌格式正确
- 检查令牌是否过期
- 验证IP绑定设置

### 日志查看

后端日志位置: `csdn-vip-bankend/log/`

## 🤝 开发指南

### 项目结构

```
csdn-vip/
├── csdn-vip-bankend/          # 后端代码
│   ├── api/                   # API路由
│   ├── models/                # 数据模型
│   ├── schemas/               # Pydantic模式
│   ├── core/                  # 核心功能
│   ├── config/                # 配置文件
│   └── utils/                 # 工具函数
├── csdn-vip-frontend/         # 前端代码
│   ├── src/
│   │   ├── views/             # 页面组件
│   │   ├── api/               # API接口
│   │   ├── stores/            # 状态管理
│   │   └── router/            # 路由配置
│   └── public/                # 静态资源
└── README.md                  # 项目说明
```

### 添加新功能

1. **后端**: 在相应的模块中添加模型、API和业务逻辑
2. **前端**: 创建页面组件和API接口
3. **测试**: 编写单元测试和集成测试

## 📄 许可证

本项目采用 MIT 许可证。

## 👥 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如有问题，请通过以下方式联系：

- 提交Issue: [GitHub Issues](link-to-issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
