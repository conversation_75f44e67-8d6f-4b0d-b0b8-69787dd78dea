import traceback
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from .base import (
    TokenExpired,
    UserError,
    AuthenticationError,
    PasswordError,
)
from tortoise.exceptions import OperationalError, IntegrityError
from .response_code import HttpStatus
from core.response import ResultResponse
from utils.logger import logger
from fastapi.exceptions import RequestValidationError, ValidationError, StarletteHTTPException
from fastapi.responses import JSONResponse


def register_exception(app: FastAPI) -> None:
    """
    全局异常捕获
    注意 别手误多敲一个s
    exception_handler
    exception_handlers
    两者有区别
        如果只捕获一个异常 启动会报错
        @exception_handlers(UserNotFound)
    TypeError: 'dict' object is not callable
    :param app:
    :return:
    """

    # 自定义异常 捕获
    @app.exception_handler(TokenExpired)
    async def token_expire_exception_handler(request: Request, exc: TokenExpired):
        """
        token过期
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"token过期{exc.err_desc}\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(content=ResultResponse[str](code=HttpStatus.HTTP_420_TOKEN_EXPIRED,
                                                        message=exc.err_desc).dict())

    @app.exception_handler(UserError)
    async def token_auth_exception_handler(request: Request, exc: UserError):
        """
        登录用户不存在
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"登录用户不存在{exc.err_desc}\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_419_USER_NOT_FOUND, message=exc.err_desc).dict())

    @app.exception_handler(AuthenticationError)
    async def authentication_exception_handler(request: Request, exc: AuthenticationError):
        """
        鉴权失败
        :param request:
        :param exc:
        :return:
        """
        logger.error(f"鉴权失败:{exc.err_desc} \n"
                     f"URL:{request.method}\t{request.url}\n"
                     f"Headers:{request.headers}\n"
                     f"Query_params:{request.query_params}\n"
                     f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_403_FORBIDDEN, message=exc.err_desc).dict())

    @app.exception_handler(PasswordError)
    async def authentication_exception_handler(request: Request, exc: PasswordError):
        """
        鉴权失败
        :param request:
        :param exc:
        :return:
        """
        logger.error(f"密码验证失败:{exc.err_desc} \n"
                     f"URL:{request.method}\t{request.url}\n"
                     f"Headers:{request.headers}\n"
                     f"Query_params:{request.query_params}\n"
                     f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_410_PASSWORD_VERIFY_ERROR, message=exc.err_desc).dict())

    @app.exception_handler(ValidationError)
    async def inner_validation_exception_handler(request: Request, exc: ValidationError):
        """
        内部参数验证异常
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"内部参数验证错误\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_421_INNER_PARAM_EXCEPT, message='内部参数校验失败').dict())

    @app.exception_handler(RequestValidationError)
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
        """
        请求参数验证异常
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"请求参数格式错误\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_422_QUERY_PARAM_EXCEPT, message='请求参数校验异常').dict())

    @app.exception_handler(OperationalError)
    async def operational_error_handler(request: Request, exc: OperationalError):
        """
        数据库操作错误
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"数据库操作错误\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_501_DATABASE_OPERATIONAL_ERROR,
                                        message='query 参数错误').dict())

    @app.exception_handler(IntegrityError)
    async def integrity_error_handler(request: Request, exc: IntegrityError):
        """
        数据插入错误
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"数据插入错误\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_502_DATABASE_DUPLICATE_ERROR,
                                        message=f'数据插入错误: {traceback.format_exc()}').dict())

    # 捕获全部异常
    @app.exception_handler(Exception)
    async def all_exception_handler(request: Request, exc: Exception):
        """
        全局所有异常
        :param request:
        :param exc:
        :return:
        """
        logger.error(
            f"全局异常\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_500_INTERNAL_SERVER_ERROR, message='服务器异常').dict())

    @app.exception_handler(RequestValidationError)  # 重写请求验证异常处理器
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
        """
        请求参数验证异常
        :param request: 请求头信息
        :param exc: 异常对象
        :return:
        """
        # 日志记录异常详细上下文
        logger.error(
            f"请求参数验证异常\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=HttpStatus.HTTP_422_QUERY_PARAM_EXCEPT, message=str(exc)).dict())

    @app.exception_handler(HTTPException)  # 重写HTTPException异常处理器
    async def http_exception_handler(request, exc: HTTPException):
        logger.error(
            f"全局异常\n"
            f"URL:{request.method}\t{request.url}\n"
            f"Headers:{request.headers}\n"
            f"Query_params:{request.query_params}\n"
            f"{traceback.format_exc()}")
        return JSONResponse(
            content=ResultResponse[str](code=exc.status_code, message=str(exc.detail)).dict())
