<template>
  <div class="extract-container">
    <div class="header-section">
      <h1>文章提取</h1>
      <div class="token-status" v-if="extractionStore.isTokenValid">
        <el-tag type="success">
          <el-icon><Check /></el-icon>
          令牌有效
        </el-tag>
        <span v-if="extractionStore.remainingCount !== null" class="remaining-info">
          剩余次数：{{ extractionStore.remainingCount }}
        </span>
      </div>
    </div>

    <div class="extract-form">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DocumentAdd /></el-icon>
            <span>提取CSDN文章</span>
          </div>
        </template>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="文章URL" prop="url">
            <el-input
              v-model="form.url"
              placeholder="请输入CSDN文章链接，如：https://blog.csdn.net/username/article/details/123456789"
              size="large"
              @keyup.enter="submitForm"
            >
              <template #prepend>
                <el-icon><Link /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="form.force_refresh">
              强制重新提取（忽略缓存）
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              @click="submitForm"
              :loading="isExtracting"
              :disabled="!extractionStore.isTokenValid"
            >
              <el-icon><Download /></el-icon>
              开始提取
            </el-button>
            <el-button size="large" @click="resetForm">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 批量提取 -->
    <div class="batch-extract">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><FolderAdd /></el-icon>
            <span>批量提取</span>
          </div>
        </template>

        <el-form>
          <el-form-item label="批量URL">
            <el-input
              v-model="batchUrls"
              type="textarea"
              :rows="5"
              placeholder="请输入多个CSDN文章链接，每行一个"
            />
          </el-form-item>

          <el-form-item>
            <el-button 
              type="primary" 
              @click="submitBatchForm"
              :loading="isBatchExtracting"
              :disabled="!extractionStore.isTokenValid"
            >
              <el-icon><FolderAdd /></el-icon>
              批量提取
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 提取结果 -->
    <div v-if="extractionResult" class="result-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>提取结果</span>
          </div>
        </template>

        <div class="result-content">
          <div class="result-header">
            <el-tag 
              :type="extractionResult.success ? 'success' : 'danger'" 
              size="large"
            >
              {{ extractionResult.success ? '提取成功' : '提取失败' }}
            </el-tag>
            <span v-if="extractionResult.is_duplicate" class="duplicate-tag">
              <el-tag type="warning">来自缓存</el-tag>
            </span>
          </div>

          <div class="result-message">
            {{ extractionResult.message }}
          </div>

          <div v-if="extractionResult.data" class="article-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="标题">
                {{ extractionResult.data.title || '未获取到标题' }}
              </el-descriptions-item>
              <el-descriptions-item label="作者">
                {{ extractionResult.data.author || '未知作者' }}
              </el-descriptions-item>
              <el-descriptions-item label="提取时间">
                {{ formatTime(extractionResult.data.request_time) }}
              </el-descriptions-item>
              <el-descriptions-item label="内容长度">
                {{ extractionResult.data.content_length || 0 }} 字符
              </el-descriptions-item>
            </el-descriptions>

            <div v-if="extractionResult.data.content" class="content-preview">
              <h4>内容预览：</h4>
              <div class="content-text">
                {{ extractionResult.data.content.substring(0, 500) }}
                <span v-if="extractionResult.data.content.length > 500">...</span>
              </div>
              <div class="content-actions">
                <el-button @click="copyContent">
                  <el-icon><CopyDocument /></el-icon>
                  复制内容
                </el-button>
                <el-button @click="downloadContent">
                  <el-icon><Download /></el-icon>
                  下载内容
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 批量提取结果 -->
    <div v-if="batchResult" class="batch-result-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>批量提取结果</span>
          </div>
        </template>

        <div class="batch-summary">
          <el-tag type="info">总数：{{ batchResult.total_count }}</el-tag>
          <el-tag type="success">成功：{{ batchResult.success_count }}</el-tag>
          <el-tag type="danger">失败：{{ batchResult.failed_count }}</el-tag>
          <el-tag type="warning">重复：{{ batchResult.duplicate_count }}</el-tag>
        </div>

        <el-table :data="batchResult.results" style="width: 100%">
          <el-table-column prop="success" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="data.title" label="标题" show-overflow-tooltip />
          <el-table-column prop="message" label="消息" show-overflow-tooltip />
          <el-table-column prop="is_duplicate" label="缓存" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.is_duplicate" type="warning" size="small">缓存</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Check, DocumentAdd, Link, Download, Refresh, FolderAdd, 
  Document, CopyDocument, DataAnalysis 
} from '@element-plus/icons-vue'
import { useExtractionStore } from '../stores/extraction'
import extractionApi from '../api/extraction'
import type { ExtractionResponse } from '../api/extraction'

const router = useRouter()
const extractionStore = useExtractionStore()

// 表单数据
const form = reactive({
  url: '',
  force_refresh: false
})

// 批量提取
const batchUrls = ref('')

// 表单验证规则
const rules: FormRules = {
  url: [
    { required: true, message: '请输入文章URL', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/(blog\.csdn\.net\/\w+\/article\/details\/\d+|\w+\.blog\.csdn\.net\/article\/details\/\d+)/, 
      message: '请输入有效的CSDN文章链接', 
      trigger: 'blur' 
    }
  ]
}

// 状态
const formRef = ref<FormInstance>()
const isExtracting = ref(false)
const isBatchExtracting = ref(false)
const extractionResult = ref<ExtractionResponse | null>(null)
const batchResult = ref<any>(null)

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  if (!extractionStore.currentToken) {
    ElMessage.error('请先验证访问令牌')
    router.push('/')
    return
  }

  isExtracting.value = true
  extractionResult.value = null

  try {
    const response = await extractionApi.extractArticle({
      url: form.url,
      token: extractionStore.currentToken,
      force_refresh: form.force_refresh
    })

    extractionResult.value = response
    
    if (response.success) {
      ElMessage.success('文章提取成功！')
      if (response.data) {
        extractionStore.addExtractionRecord(response.data)
      }
    } else {
      ElMessage.error(response.message || '提取失败')
    }
  } catch (error) {
    console.error('Extraction failed:', error)
    ElMessage.error('提取失败，请稍后重试')
  } finally {
    isExtracting.value = false
  }
}

// 批量提取
const submitBatchForm = async () => {
  if (!batchUrls.value.trim()) {
    ElMessage.warning('请输入要提取的URL')
    return
  }

  if (!extractionStore.currentToken) {
    ElMessage.error('请先验证访问令牌')
    router.push('/')
    return
  }

  const urls = batchUrls.value
    .split('\n')
    .map(url => url.trim())
    .filter(url => url.length > 0)

  if (urls.length === 0) {
    ElMessage.warning('请输入有效的URL')
    return
  }

  if (urls.length > 10) {
    ElMessage.warning('批量提取最多支持10个URL')
    return
  }

  isBatchExtracting.value = true
  batchResult.value = null

  try {
    const response = await extractionApi.batchExtractArticles({
      urls: urls,
      token: extractionStore.currentToken,
      force_refresh: form.force_refresh
    })

    batchResult.value = response
    ElMessage.success(`批量提取完成：成功 ${response.success_count}，失败 ${response.failed_count}`)
  } catch (error) {
    console.error('Batch extraction failed:', error)
    ElMessage.error('批量提取失败，请稍后重试')
  } finally {
    isBatchExtracting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  extractionResult.value = null
}

// 复制内容
const copyContent = async () => {
  if (!extractionResult.value?.data?.content) return

  try {
    await navigator.clipboard.writeText(extractionResult.value.data.content)
    ElMessage.success('内容已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 下载内容
const downloadContent = () => {
  if (!extractionResult.value?.data?.content) return

  const content = extractionResult.value.data.content
  const title = extractionResult.value.data.title || 'article'
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${title}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('文件下载已开始')
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 页面初始化
onMounted(async () => {
  // 检查token有效性
  if (!extractionStore.currentToken) {
    ElMessage.warning('请先验证访问令牌')
    router.push('/')
    return
  }

  // 如果没有token信息，重新验证
  if (!extractionStore.tokenInfo) {
    try {
      const response = await extractionApi.validateToken({
        token: extractionStore.currentToken
      })

      if (!response.valid) {
        ElMessage.error('令牌已失效，请重新验证')
        extractionStore.clearAll()
        router.push('/')
        return
      }

      extractionStore.setTokenInfo(response)
    } catch (error) {
      console.error('Token validation failed:', error)
      ElMessage.error('令牌验证失败')
      router.push('/')
    }
  }
})
</script>

<style scoped>
.extract-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h1 {
  margin: 0;
  color: #303133;
}

.token-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.remaining-info {
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.extract-form,
.batch-extract,
.result-section,
.batch-result-section {
  margin-bottom: 2rem;
}

.result-content {
  space-y: 1rem;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.duplicate-tag {
  margin-left: 1rem;
}

.result-message {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f5f7fa;
  border-radius: 8px;
  color: #606266;
}

.article-info {
  margin-top: 1rem;
}

.content-preview {
  margin-top: 1rem;
}

.content-preview h4 {
  margin-bottom: 0.5rem;
  color: #303133;
}

.content-text {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.content-actions {
  display: flex;
  gap: 1rem;
}

.batch-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .extract-container {
    padding: 1rem;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .content-actions {
    flex-direction: column;
  }
}
</style>
