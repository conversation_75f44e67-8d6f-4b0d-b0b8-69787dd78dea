import os
import sys
import time
import logging
from loguru import logger
from config import settings
from pprint import pformat



class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def format_record(record: dict) -> str:
    format_string = '<level>{level: <8}</level>  <green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> - <cyan>{name}</cyan>:<cyan>{function}</cyan> - <level>{message}</level>'

    if record["extra"].get("payload") is not None:
        record["extra"]["payload"] = pformat(
            record["extra"]["payload"], indent=4, compact=True, width=88
        )
        format_string += "\n<level>{extra[payload]}</level>"

    format_string += "{exception}\n"
    return format_string

def init_logger() -> None:
    logging.getLogger().handlers = [InterceptHandler()]
    logger.configure(
        handlers=[{"sink": sys.stdout, "level": logging.DEBUG, "format": format_record}])
    logger.debug('日志系统已加载')
    logging.getLogger("uvicorn.access").handlers = [InterceptHandler()]

# 定位到log日志文件
log_path = settings.APP.LOG_PATH

if not os.path.exists(log_path):
    os.mkdir(log_path)

log_path_info = os.path.join(log_path, f'{time.strftime("%Y-%m-%d")}_info.log')
log_path_warning = os.path.join(log_path,
                                f'{time.strftime("%Y-%m-%d")}_warning.log')
log_path_error = os.path.join(log_path,
                              f'{time.strftime("%Y-%m-%d")}_error.log')

# 日志简单配置 文件区分不同级别的日志
logger.add(log_path_info,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='INFO',
           compression='zip',
           retention=5)

logger.add(log_path_warning,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='WARNING',
           compression='zip',
           retention=5)

logger.add(log_path_error,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='ERROR',
           compression='zip',
           retention=5)



__all__ = ["logger", "init_logger"]