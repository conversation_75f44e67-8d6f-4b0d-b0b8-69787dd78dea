<template>
  <div class="system-settings">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基础设置 -->
      <el-tab-pane label="基础设置" name="basic">
        <el-form 
          :model="basicSettings" 
          :rules="basicRules" 
          ref="basicFormRef" 
          label-width="150px"
        >
          <el-form-item label="系统名称" prop="system_name">
            <el-input v-model="basicSettings.system_name" placeholder="请输入系统名称" />
          </el-form-item>
          
          <el-form-item label="系统描述" prop="system_description">
            <el-input 
              v-model="basicSettings.system_description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入系统描述"
            />
          </el-form-item>
          
          <el-form-item label="系统URL" prop="system_url">
            <el-input v-model="basicSettings.system_url" placeholder="https://example.com" />
          </el-form-item>
          
          <el-form-item label="联系邮箱" prop="contact_email">
            <el-input v-model="basicSettings.contact_email" placeholder="<EMAIL>" />
          </el-form-item>
          
          <el-form-item label="维护模式">
            <el-switch 
              v-model="basicSettings.maintenance_mode" 
              active-text="开启"
              inactive-text="关闭"
            />
            <span class="form-tip">开启后，只有管理员可以访问系统</span>
          </el-form-item>
          
          <el-form-item label="用户注册">
            <el-switch 
              v-model="basicSettings.allow_registration" 
              active-text="允许"
              inactive-text="禁止"
            />
            <span class="form-tip">是否允许新用户注册</span>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveBasicSettings" :loading="isSavingBasic">
              保存设置
            </el-button>
            <el-button @click="resetBasicSettings">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 提取设置 -->
      <el-tab-pane label="提取设置" name="extraction">
        <el-form 
          :model="extractionSettings" 
          :rules="extractionRules" 
          ref="extractionFormRef" 
          label-width="150px"
        >
          <el-form-item label="默认超时时间" prop="default_timeout">
            <el-input-number 
              v-model="extractionSettings.default_timeout" 
              :min="10" 
              :max="300"
              placeholder="秒"
            />
            <span class="form-tip">秒（10-300秒）</span>
          </el-form-item>
          
          <el-form-item label="最大重试次数" prop="max_retries">
            <el-input-number 
              v-model="extractionSettings.max_retries" 
              :min="0" 
              :max="10"
            />
            <span class="form-tip">提取失败时的最大重试次数</span>
          </el-form-item>
          
          <el-form-item label="并发限制" prop="concurrent_limit">
            <el-input-number 
              v-model="extractionSettings.concurrent_limit" 
              :min="1" 
              :max="100"
            />
            <span class="form-tip">同时进行的最大提取任务数</span>
          </el-form-item>
          
          <el-form-item label="缓存过期时间" prop="cache_expire_hours">
            <el-input-number 
              v-model="extractionSettings.cache_expire_hours" 
              :min="1" 
              :max="168"
            />
            <span class="form-tip">小时（1-168小时）</span>
          </el-form-item>
          
          <el-form-item label="自动清理周期" prop="cleanup_days">
            <el-input-number 
              v-model="extractionSettings.cleanup_days" 
              :min="1" 
              :max="365"
            />
            <span class="form-tip">天（自动清理多少天前的记录）</span>
          </el-form-item>
          
          <el-form-item label="启用内容压缩">
            <el-switch 
              v-model="extractionSettings.enable_compression" 
              active-text="开启"
              inactive-text="关闭"
            />
            <span class="form-tip">压缩提取的内容以节省存储空间</span>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveExtractionSettings" :loading="isSavingExtraction">
              保存设置
            </el-button>
            <el-button @click="resetExtractionSettings">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 安全设置 -->
      <el-tab-pane label="安全设置" name="security">
        <el-form 
          :model="securitySettings" 
          :rules="securityRules" 
          ref="securityFormRef" 
          label-width="150px"
        >
          <el-form-item label="密码最小长度" prop="password_min_length">
            <el-input-number 
              v-model="securitySettings.password_min_length" 
              :min="6" 
              :max="20"
            />
            <span class="form-tip">位（6-20位）</span>
          </el-form-item>
          
          <el-form-item label="密码复杂度">
            <el-checkbox-group v-model="securitySettings.password_requirements">
              <el-checkbox value="uppercase">包含大写字母</el-checkbox>
              <el-checkbox value="lowercase">包含小写字母</el-checkbox>
              <el-checkbox value="numbers">包含数字</el-checkbox>
              <el-checkbox value="symbols">包含特殊字符</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="登录失败限制" prop="login_max_attempts">
            <el-input-number 
              v-model="securitySettings.login_max_attempts" 
              :min="3" 
              :max="10"
            />
            <span class="form-tip">次（超过后将锁定账户）</span>
          </el-form-item>
          
          <el-form-item label="账户锁定时间" prop="lockout_duration">
            <el-input-number 
              v-model="securitySettings.lockout_duration" 
              :min="5" 
              :max="1440"
            />
            <span class="form-tip">分钟（5-1440分钟）</span>
          </el-form-item>
          
          <el-form-item label="会话超时时间" prop="session_timeout">
            <el-input-number 
              v-model="securitySettings.session_timeout" 
              :min="30" 
              :max="480"
            />
            <span class="form-tip">分钟（30-480分钟）</span>
          </el-form-item>
          
          <el-form-item label="强制HTTPS">
            <el-switch 
              v-model="securitySettings.force_https" 
              active-text="开启"
              inactive-text="关闭"
            />
            <span class="form-tip">强制使用HTTPS协议</span>
          </el-form-item>
          
          <el-form-item label="启用审计日志">
            <el-switch 
              v-model="securitySettings.enable_audit_log" 
              active-text="开启"
              inactive-text="关闭"
            />
            <span class="form-tip">记录用户操作日志</span>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveSecuritySettings" :loading="isSavingSecurity">
              保存设置
            </el-button>
            <el-button @click="resetSecuritySettings">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 限流设置 -->
      <el-tab-pane label="限流设置" name="rate-limit">
        <div class="rate-limit-section">
          <h4>API限流规则</h4>
          <div class="rate-limit-rules">
            <el-card v-for="(rule, index) in rateLimitRules" :key="index" class="rule-card">
              <template #header>
                <div class="rule-header">
                  <span>{{ rule.name }}</span>
                  <el-switch v-model="rule.enabled" />
                </div>
              </template>
              
              <el-form :model="rule" label-width="120px">
                <el-form-item label="请求路径">
                  <el-input v-model="rule.path" placeholder="/api/*" />
                </el-form-item>
                
                <el-form-item label="限制类型">
                  <el-select v-model="rule.limit_type">
                    <el-option label="按IP限制" value="ip" />
                    <el-option label="按用户限制" value="user" />
                    <el-option label="按Token限制" value="token" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="请求频率">
                  <el-input-number v-model="rule.requests_per_minute" :min="1" :max="1000" />
                  <span class="form-tip">次/分钟</span>
                </el-form-item>
                
                <el-form-item label="突发限制">
                  <el-input-number v-model="rule.burst_limit" :min="1" :max="100" />
                  <span class="form-tip">短时间内最大请求数</span>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="danger" @click="deleteRateLimitRule(index)">
                    删除规则
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          
          <div class="add-rule-section">
            <el-button type="primary" @click="addRateLimitRule">
              <el-icon><Plus /></el-icon>
              添加限流规则
            </el-button>
            <el-button @click="saveRateLimitSettings" :loading="isSavingRateLimit">
              保存所有规则
            </el-button>
          </div>
        </div>
      </el-tab-pane>
      
      <!-- 邮件设置 -->
      <el-tab-pane label="邮件设置" name="email">
        <el-form 
          :model="emailSettings" 
          :rules="emailRules" 
          ref="emailFormRef" 
          label-width="150px"
        >
          <el-form-item label="SMTP服务器" prop="smtp_host">
            <el-input v-model="emailSettings.smtp_host" placeholder="smtp.gmail.com" />
          </el-form-item>
          
          <el-form-item label="SMTP端口" prop="smtp_port">
            <el-input-number v-model="emailSettings.smtp_port" :min="1" :max="65535" />
          </el-form-item>
          
          <el-form-item label="发件人邮箱" prop="sender_email">
            <el-input v-model="emailSettings.sender_email" placeholder="<EMAIL>" />
          </el-form-item>
          
          <el-form-item label="发件人名称" prop="sender_name">
            <el-input v-model="emailSettings.sender_name" placeholder="CSDN提取系统" />
          </el-form-item>
          
          <el-form-item label="SMTP用户名" prop="smtp_username">
            <el-input v-model="emailSettings.smtp_username" placeholder="用户名" />
          </el-form-item>
          
          <el-form-item label="SMTP密码" prop="smtp_password">
            <el-input 
              v-model="emailSettings.smtp_password" 
              type="password" 
              show-password
              placeholder="密码或应用密码"
            />
          </el-form-item>
          
          <el-form-item label="加密方式">
            <el-select v-model="emailSettings.encryption_type">
              <el-option label="无加密" value="none" />
              <el-option label="TLS" value="tls" />
              <el-option label="SSL" value="ssl" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="启用邮件通知">
            <el-switch 
              v-model="emailSettings.enabled" 
              active-text="开启"
              inactive-text="关闭"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveEmailSettings" :loading="isSavingEmail">
              保存设置
            </el-button>
            <el-button @click="testEmailSettings" :loading="isTestingEmail">
              发送测试邮件
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 日志设置 -->
      <el-tab-pane label="日志设置" name="logging">
        <el-form 
          :model="loggingSettings" 
          label-width="150px"
        >
          <el-form-item label="日志级别">
            <el-select v-model="loggingSettings.log_level">
              <el-option label="DEBUG" value="debug" />
              <el-option label="INFO" value="info" />
              <el-option label="WARNING" value="warning" />
              <el-option label="ERROR" value="error" />
              <el-option label="CRITICAL" value="critical" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="日志保留天数">
            <el-input-number 
              v-model="loggingSettings.retention_days" 
              :min="1" 
              :max="365"
            />
            <span class="form-tip">天（1-365天）</span>
          </el-form-item>
          
          <el-form-item label="最大日志文件大小">
            <el-input-number 
              v-model="loggingSettings.max_file_size_mb" 
              :min="1" 
              :max="1024"
            />
            <span class="form-tip">MB（1-1024MB）</span>
          </el-form-item>
          
          <el-form-item label="启用访问日志">
            <el-switch 
              v-model="loggingSettings.enable_access_log" 
              active-text="开启"
              inactive-text="关闭"
            />
          </el-form-item>
          
          <el-form-item label="启用错误日志">
            <el-switch 
              v-model="loggingSettings.enable_error_log" 
              active-text="开启"
              inactive-text="关闭"
            />
          </el-form-item>
          
          <el-form-item label="启用调试日志">
            <el-switch 
              v-model="loggingSettings.enable_debug_log" 
              active-text="开启"
              inactive-text="关闭"
            />
            <span class="form-tip">仅在开发环境下建议开启</span>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveLoggingSettings" :loading="isSavingLogging">
              保存设置
            </el-button>
            <el-button @click="downloadLogs" :loading="isDownloadingLogs">
              下载日志文件
            </el-button>
            <el-button type="danger" @click="clearLogs">
              清空日志
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const activeTab = ref('basic')

// 加载状态
const isSavingBasic = ref(false)
const isSavingExtraction = ref(false)
const isSavingSecurity = ref(false)
const isSavingRateLimit = ref(false)
const isSavingEmail = ref(false)
const isSavingLogging = ref(false)
const isTestingEmail = ref(false)
const isDownloadingLogs = ref(false)

// 基础设置
const basicSettings = reactive({
  system_name: 'CSDN文章提取系统',
  system_description: '高效、稳定的CSDN文章内容提取服务',
  system_url: 'https://csdn-extract.example.com',
  contact_email: '<EMAIL>',
  maintenance_mode: false,
  allow_registration: true
})

const basicFormRef = ref<FormInstance>()

const basicRules: FormRules = {
  system_name: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  system_url: [
    { required: true, message: '请输入系统URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  contact_email: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 提取设置
const extractionSettings = reactive({
  default_timeout: 30,
  max_retries: 3,
  concurrent_limit: 10,
  cache_expire_hours: 24,
  cleanup_days: 30,
  enable_compression: true
})

const extractionFormRef = ref<FormInstance>()

const extractionRules: FormRules = {
  default_timeout: [
    { required: true, message: '请设置默认超时时间', trigger: 'blur' }
  ],
  max_retries: [
    { required: true, message: '请设置最大重试次数', trigger: 'blur' }
  ],
  concurrent_limit: [
    { required: true, message: '请设置并发限制', trigger: 'blur' }
  ]
}

// 安全设置
const securitySettings = reactive({
  password_min_length: 8,
  password_requirements: ['lowercase', 'numbers'],
  login_max_attempts: 5,
  lockout_duration: 30,
  session_timeout: 120,
  force_https: true,
  enable_audit_log: true
})

const securityFormRef = ref<FormInstance>()

const securityRules: FormRules = {
  password_min_length: [
    { required: true, message: '请设置密码最小长度', trigger: 'blur' }
  ],
  login_max_attempts: [
    { required: true, message: '请设置登录失败限制', trigger: 'blur' }
  ]
}

// 限流设置
const rateLimitRules = ref([
  {
    name: '全局API限流',
    path: '/api/*',
    limit_type: 'ip',
    requests_per_minute: 60,
    burst_limit: 10,
    enabled: true
  },
  {
    name: '提取接口限流',
    path: '/api/v1/extraction/*',
    limit_type: 'token',
    requests_per_minute: 10,
    burst_limit: 3,
    enabled: true
  }
])

// 邮件设置
const emailSettings = reactive({
  smtp_host: 'smtp.gmail.com',
  smtp_port: 587,
  sender_email: '<EMAIL>',
  sender_name: 'CSDN提取系统',
  smtp_username: '',
  smtp_password: '',
  encryption_type: 'tls',
  enabled: false
})

const emailFormRef = ref<FormInstance>()

const emailRules: FormRules = {
  smtp_host: [
    { required: true, message: '请输入SMTP服务器', trigger: 'blur' }
  ],
  smtp_port: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' }
  ],
  sender_email: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 日志设置
const loggingSettings = reactive({
  log_level: 'info',
  retention_days: 30,
  max_file_size_mb: 100,
  enable_access_log: true,
  enable_error_log: true,
  enable_debug_log: false
})

// 保存基础设置
const saveBasicSettings = async () => {
  if (!basicFormRef.value) return
  
  try {
    const valid = await basicFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSavingBasic.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    console.error('Save basic settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingBasic.value = false
  }
}

// 重置基础设置
const resetBasicSettings = () => {
  Object.assign(basicSettings, {
    system_name: 'CSDN文章提取系统',
    system_description: '高效、稳定的CSDN文章内容提取服务',
    system_url: 'https://csdn-extract.example.com',
    contact_email: '<EMAIL>',
    maintenance_mode: false,
    allow_registration: true
  })
}

// 保存提取设置
const saveExtractionSettings = async () => {
  if (!extractionFormRef.value) return
  
  try {
    const valid = await extractionFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSavingExtraction.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('提取设置保存成功')
  } catch (error) {
    console.error('Save extraction settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingExtraction.value = false
  }
}

// 重置提取设置
const resetExtractionSettings = () => {
  Object.assign(extractionSettings, {
    default_timeout: 30,
    max_retries: 3,
    concurrent_limit: 10,
    cache_expire_hours: 24,
    cleanup_days: 30,
    enable_compression: true
  })
}

// 保存安全设置
const saveSecuritySettings = async () => {
  if (!securityFormRef.value) return
  
  try {
    const valid = await securityFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSavingSecurity.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    console.error('Save security settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingSecurity.value = false
  }
}

// 重置安全设置
const resetSecuritySettings = () => {
  Object.assign(securitySettings, {
    password_min_length: 8,
    password_requirements: ['lowercase', 'numbers'],
    login_max_attempts: 5,
    lockout_duration: 30,
    session_timeout: 120,
    force_https: true,
    enable_audit_log: true
  })
}

// 添加限流规则
const addRateLimitRule = () => {
  rateLimitRules.value.push({
    name: '新限流规则',
    path: '/api/*',
    limit_type: 'ip',
    requests_per_minute: 60,
    burst_limit: 10,
    enabled: true
  })
}

// 删除限流规则
const deleteRateLimitRule = (index: number) => {
  rateLimitRules.value.splice(index, 1)
}

// 保存限流设置
const saveRateLimitSettings = async () => {
  isSavingRateLimit.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('限流设置保存成功')
  } catch (error) {
    console.error('Save rate limit settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingRateLimit.value = false
  }
}

// 保存邮件设置
const saveEmailSettings = async () => {
  if (!emailFormRef.value) return
  
  try {
    const valid = await emailFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSavingEmail.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('邮件设置保存成功')
  } catch (error) {
    console.error('Save email settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingEmail.value = false
  }
}

// 测试邮件设置
const testEmailSettings = async () => {
  isTestingEmail.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    console.error('Test email failed:', error)
    ElMessage.error('测试邮件发送失败')
  } finally {
    isTestingEmail.value = false
  }
}

// 保存日志设置
const saveLoggingSettings = async () => {
  isSavingLogging.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志设置保存成功')
  } catch (error) {
    console.error('Save logging settings failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSavingLogging.value = false
  }
}

// 下载日志文件
const downloadLogs = async () => {
  isDownloadingLogs.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('日志文件下载开始')
  } catch (error) {
    console.error('Download logs failed:', error)
    ElMessage.error('下载失败')
  } finally {
    isDownloadingLogs.value = false
  }
}

// 清空日志
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志文件吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('日志文件已清空')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.system-settings {
  height: 100%;
}

.form-tip {
  margin-left: 0.5rem;
  color: #909399;
  font-size: 14px;
}

.rate-limit-section {
  margin-top: 1rem;
}

.rate-limit-section h4 {
  margin-bottom: 1rem;
  color: #303133;
}

.rate-limit-rules {
  margin-bottom: 2rem;
}

.rule-card {
  margin-bottom: 1rem;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-rule-section {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #fafafa;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .add-rule-section {
    flex-direction: column;
  }
}
</style>
