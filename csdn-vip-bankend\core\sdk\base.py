import json
import random
from typing import Dict, Iterable


class BaseApi:
    @staticmethod
    def _ordered_data(data: Dict) -> Iterable:
        """
        将所有非空值请求参数名按照 ASCII 码顺序从小到大(a-z)排序
        @param data:
        @return:
        """
        for k, v in data.items():
            if isinstance(v, dict):
                # 将字典类型的数据dump出来
                data[k] = json.dumps(v, separators=(',', ':'))
        return sorted(data.items())

    @staticmethod
    def _generate_random_str(random_length: int = 16) -> str:
        """
        生成一个指定长度的随机字符串
        """
        random_str = ''
        base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789'
        length = len(base_str) - 1
        for i in range(random_length):
            random_str += base_str[random.randint(0, length)]
        return random_str

    @staticmethod
    def _ignore_none(**kwargs):
        """
        去除None的查询字段
        @param kwargs:
        @return:
        """
        return {key: value for key, value in kwargs.items() if value is not None}