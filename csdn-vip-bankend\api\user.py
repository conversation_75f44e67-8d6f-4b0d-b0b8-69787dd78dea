from models import User
from schemas import UserCreate, UserOut
from typing import Any
from fastapi import APIRouter, Depends
from core.auth.base import get_password_hash, get_current_user
from core.response import ResultResponse

user_router = APIRouter(
    prefix="/user",
    tags=["用户"],
)


@user_router.get(
    "",
    name="UserInfo",
    summary="UserInfo",
    response_model=ResultResponse[UserOut]
)
async def get_user(current_user: User = Depends(get_current_user)) -> ResultResponse[UserOut]:
    current_user = UserOut.from_orm(current_user)
    return ResultResponse[UserOut](result=current_user)


@user_router.post(
    "",
    name="Create One",
    summary="Create One",
    response_model=ResultResponse[UserOut]
)
async def create_user(user: UserCreate,  ) -> Any:
    """
    Test access token
    """
    # 过滤并获取项目数据表的数据
    # approval_ids = await ProjectResource.filter(approve_user=username).values("approval")  # 通过当前用户id找的流程id


    current_user = await User.create(
        username=user.username,
        hashed_password=get_password_hash(user.password),
        is_active=True
    )
    current_user = UserOut.from_orm(current_user)
    return ResultResponse[UserOut](result=current_user)
