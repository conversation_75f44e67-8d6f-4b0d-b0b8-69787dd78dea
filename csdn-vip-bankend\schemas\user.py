from typing import Optional
from utils.base import PasswordTool
from pydantic import BaseModel, Field, ValidationError, validator
from models import User
from utils.model_pydantic import pydantic_model_creator
# 过滤掉敏感信息，然后传递，用于公开显示
UserOut = pydantic_model_creator(
    User,
    name="UserOut",
    exclude=(
        'hashed_password', # 该omr
    )
)


class UserCreate(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    confirm: str = Field(..., description='确认新密码')

    # 联合验证
    @validator('confirm')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValidationError('两次输入的密码不一致')
        return v

    @validator('password')
    def passwords_validation(cls, v):
        pt = PasswordTool(v)
        pt.process_password()
        return v

    class Config:
        schema_extra = {
            'example': {
                'username': 'admin001',
                'password': 'Zhang2513!',
                'confirm': 'Zhang2513!',
            }
        }
