from typing import List, Dict
from utils.base import PasswordTool
from pydantic import BaseModel, Field, ValidationError, validator


class BaseQueryModel:
    def to_dict(self, exclude: List = None, exclude_none: bool = False) -> Dict:
        result = dict()
        for key, value in self.__dict__.items():
            if exclude is not None and key in exclude:
                continue
            if exclude_none and value is not None:
                result[key] = value
        return result


class BaseListQueryModel(BaseQueryModel):
    def __init__(self, limit, skip):
        self.limit = limit
        self.skip = skip



class PasswordBase(BaseModel):
    password: str = Field(..., description='新密码')
    confirm: str = Field(..., description='确认新密码')

    # 联合验证
    @validator('confirm')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValidationError('两次输入的密码不一致')
        return v

    @validator('password')
    def passwords_validation(cls, v):
        pt = PasswordTool(v)
        pt.process_password()
        return v
