import datetime

from models import Student, Teacher, Books, BooksVersion
from utils.model_pydantic import pydantic_model_creator
from typing import List, Optional
from pydantic import BaseModel, Field, validator
from schemas.base import BaseListQueryModel, BaseQueryModel, ValidationError
from fastapi import Query
from utils.base import PasswordTool

StudentOut = pydantic_model_creator(
    Student,
    name="StudentOut",
    exclude_backward_relations=False,
    exclude=('hashed_password', 'create_time', 'update_time')
)

StudentCreateBase = pydantic_model_creator(
    Student,
    name="StudentCreateBase",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'remark', "uuid"
    )
)

StudentUpdate = pydantic_model_creator(
    Student,
    name="StudentUpdate",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'remark', "uuid"
    )
)

TeacherOut = pydantic_model_creator(
    Teacher,
    name="TeacherOut",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'remark', "uuid"
    )
)
BooksOut = pydantic_model_creator(
    Books,
    exclude_relations=False,
    exclude_backward_relations=False,
    name="BooksOut",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'remark', "uuid"
    )
)
BooksVersionOut = pydantic_model_creator(
    BooksVersion,
    name="BooksVersionOut",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'remark', "uuid"
    )
)


class BooksAdd(BaseModel):
    book_name: Optional[str] = None,  # Field(..., description='权限列表')
    book_stu_id: Optional[int] = None,  # Field(..., description='权限列表')
    time: Optional[datetime.datetime] = None


class BooksGet(BaseQueryModel):
    def __init__(
            self,
            ids: int = None
    ):
        self.ids = ids


class StudentGet(BaseListQueryModel):
    def __init__(
            self,
            limit: Optional[int] = Query(default=None),
            skip: int = Query(default=0),
            ids: Optional[int] = Query(default=None, description='学生id'),
    ):
        self.ids = ids
        super().__init__(
            limit=limit,
            skip=skip,
        )


#################
# 下方是一些其他常用的不同schema的定义方式

class Demo1(StudentCreateBase):  # 通过集成前面的StudentCreateBase来便捷添加 新增 的字段
    student_number: Optional[List[int]] = Field(..., description="定义接收类型为列表，里面是数字")
    form_json: Optional[dict] = Field(..., description="定义接收类型为字典")
    group_id: Optional[int] = Field(..., description="定义接收类型为整形")
    act: Optional[str] = Field(..., description='定义接收字符串--选填（有Optional）')
    policies: List[dict] = Field(..., description='定义列表套字典')
    table_size: float = Field(..., description="定义浮点型--必填（无Optional）")
    new_info: dict = Field(..., description='定义字典--必填')
    time: datetime.datetime = Field(..., description='定义字典--必填')
    class Config:
        """schema_extra中设置参数的例子，相当于给个例子参考"""
        schema_extra = {
            "role_id": 1,
            "student_number": [],
            "form_json": {},
            "group_id": 1,
            "act": "",
            "policies": [
                {
                    "type": "api",
                    "obj": "/admin_user",
                    "acts": ["GET", "POST", "PUT", "DELETE"]
                },
            ]
        }


class Demo2(BaseModel):  # 直接赋初始值；使用Optional，让其在传输的时候是可选字段，不用必传
    status: Optional[str] = None
    position: Optional[int] = None  # 可选
    csv_up: bool = None             # 必填

class Demo3(BaseModel):
    sub: Optional[str] = Field(..., description='主题(访问实体)')
    obj: Optional[str] = Field(..., description='目标(访问资源)')
    act: Optional[str] = Field(..., description='动作(访问方式)')

    class Config:
        """schema_extra中设置参数的例子，在API文档中可以看到"""
        schema_extra = {
            'example': {
                'sub': '管理员',
                'obj': '/auth',
                'act': 'get'
            }
        }


class Demo4(Demo3):
    type: Optional[str] = Field(..., description='字符串类型')  # 继承Demo3的参数与属性，方便添加
    obj_acts: List[Demo2] = Field(..., description='可以一个字段内，直接继承上一个定义好的类')
    dashboards_list: List[StudentCreateBase] = Field(..., description="直接继承基础定义好的基础schema")

class PasswordBase(BaseModel):
    password: str = Field(..., description='新密码')
    confirm: str = Field(..., description='确认新密码')

    # 联合验证
    @validator('confirm')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValidationError('两次输入的密码不一致')
        return v

    @validator('password')
    def passwords_validation(cls, v):
        pt = PasswordTool(v)
        pt.process_password()
        return v



