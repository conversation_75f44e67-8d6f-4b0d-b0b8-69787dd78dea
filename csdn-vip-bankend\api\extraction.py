from models import (
    AccessToken, ExtractionRecord, ExtractionCore, TokenUsageLog,
    ExtractionStatus, UsageType, UsageStatus, CoreStatus
)
from schemas import (
    ExtractionRequest, ExtractionResponse, ExtractionRecordOut, ExtractionRecordSimple,
    BatchExtractionRequest, BatchExtractionResponse, ExtractionHistoryRequest,
    ExtractionHistoryResponse, ExtractionQuery, ExtractionRecordAdmin,
    ExtractionStats, ExtractionStatsRequest
)
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Request
from core.response import ResultResponse
from core.exce.base import UserError
from core.auth.base import get_current_active_user
from models import AdminUser
from datetime import datetime, timedelta
import httpx
import asyncio
import time


extraction_router = APIRouter(
    prefix="/extraction",
    tags=["文章提取"],
)


async def get_token_from_request(request_data) -> AccessToken:
    """从请求中获取并验证token"""
    token = await AccessToken.get_or_none(token=request_data.token)
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')
    
    if not token.is_valid():
        raise UserError('访问令牌已失效')
    
    return token


async def check_rate_limits(token: AccessToken, client_ip: str = None):
    """检查速率限制"""
    # 检查每小时限制
    if not await TokenUsageLog.check_rate_limit(
        token.id, client_ip, 1, token.max_hourly_usage
    ):
        raise UserError('超出每小时使用限制')
    
    # 检查每日限制
    if not await TokenUsageLog.check_daily_limit(token.id, token.max_daily_usage):
        raise UserError('超出每日使用限制')


async def call_extraction_core(url: str, auth_token: str, api_endpoint: str, timeout: int = 30) -> dict:
    """调用提取核心API"""
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.post(
            api_endpoint,
            json={'url': url, 'token': auth_token},
            headers={'Content-Type': 'application/json'}
        )
        response.raise_for_status()
        return response.json()


async def extract_single_article(
    url: str, 
    token: AccessToken, 
    client_ip: str = None, 
    user_agent: str = None,
    force_refresh: bool = False
) -> ExtractionRecord:
    """提取单篇文章"""
    
    # 检查是否已存在
    url_hash = ExtractionRecord.generate_url_hash(url)
    existing_record = await ExtractionRecord.get_by_url(url)
    
    if existing_record and existing_record.status == ExtractionStatus.SUCCESS and not force_refresh:
        # 创建新的记录标记为重复
        record = await ExtractionRecord.create(
            url=url,
            url_hash=url_hash,
            access_token=token,
            client_ip=client_ip,
            user_agent=user_agent
        )
        await record.mark_duplicate(existing_record)
        return record
    
    # 创建新的提取记录
    record = await ExtractionRecord.create(
        url=url,
        url_hash=url_hash,
        access_token=token,
        client_ip=client_ip,
        user_agent=user_agent
    )
    
    try:
        # 标记为处理中
        record.status = ExtractionStatus.PROCESSING
        await record.save()
        
        # 获取可用的核心服务
        core = await ExtractionCore.get_best_core()
        if not core:
            raise Exception('暂无可用的提取核心服务')
        
        # 增加核心服务请求计数
        await core.increment_request()
        
        try:
            # 调用核心API
            start_time = time.time()
            result = await call_extraction_core(
                url, core.auth_token, core.api_endpoint, core.timeout_seconds
            )
            response_time = time.time() - start_time
            
            # 记录成功
            await core.record_success(response_time)
            
            # 更新提取记录
            await record.mark_success(
                title=result.get('title'),
                author=result.get('author'),
                content=result.get('content')
            )
            record.core_response_time = response_time
            record.core_server = core.name
            await record.save()
            
        except Exception as e:
            # 记录核心服务失败
            await core.record_failure()
            raise e
        
        # 增加token使用次数
        await token.increment_usage()
        
    except Exception as e:
        # 标记为失败
        await record.mark_failed(str(e))
        
        # 记录使用日志
        await TokenUsageLog.log_usage(
            token_id=token.id,
            extraction_record_id=record.id,
            usage_type=UsageType.EXTRACTION,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=url,
            error_message=str(e)
        )
        raise UserError(f'文章提取失败: {str(e)}')
    
    # 记录成功使用日志
    await TokenUsageLog.log_usage(
        token_id=token.id,
        extraction_record_id=record.id,
        usage_type=UsageType.EXTRACTION,
        status=UsageStatus.SUCCESS,
        client_ip=client_ip,
        user_agent=user_agent,
        request_url=url,
        response_time=record.core_response_time
    )
    
    return record


@extraction_router.post(
    "/extract",
    name="Extract Article",
    summary="提取CSDN文章",
    response_model=ResultResponse[ExtractionResponse]
)
async def extract_article(
    request: ExtractionRequest,
    http_request: Request
) -> ResultResponse[ExtractionResponse]:
    """提取单篇CSDN文章"""
    
    # 获取客户端信息
    client_ip = http_request.client.host
    user_agent = http_request.headers.get('user-agent')
    
    # 验证token
    token = await get_token_from_request(request)
    
    # 检查速率限制
    await check_rate_limits(token, client_ip)
    
    try:
        # 执行提取
        record = await extract_single_article(
            request.url, token, client_ip, user_agent, request.force_refresh
        )
        
        # 构建响应
        if record.status == ExtractionStatus.SUCCESS:
            response = ExtractionResponse(
                success=True,
                message="文章提取成功",
                data=ExtractionRecordOut.from_orm(record),
                is_duplicate=False
            )
        elif record.status == ExtractionStatus.DUPLICATE:
            response = ExtractionResponse(
                success=True,
                message="文章已存在（从缓存获取）",
                data=ExtractionRecordOut.from_orm(record),
                is_duplicate=True
            )
        else:
            response = ExtractionResponse(
                success=False,
                message="文章提取失败",
                error_code="EXTRACTION_FAILED"
            )
        
        return ResultResponse[ExtractionResponse](result=response)
        
    except UserError as e:
        response = ExtractionResponse(
            success=False,
            message=str(e),
            error_code="USER_ERROR"
        )
        return ResultResponse[ExtractionResponse](result=response)
    except Exception as e:
        response = ExtractionResponse(
            success=False,
            message=f"系统错误: {str(e)}",
            error_code="SYSTEM_ERROR"
        )
        return ResultResponse[ExtractionResponse](result=response)


@extraction_router.post(
    "/batch-extract",
    name="Batch Extract Articles",
    summary="批量提取CSDN文章",
    response_model=ResultResponse[BatchExtractionResponse]
)
async def batch_extract_articles(
    request: BatchExtractionRequest,
    http_request: Request
) -> ResultResponse[BatchExtractionResponse]:
    """批量提取CSDN文章"""
    
    # 获取客户端信息
    client_ip = http_request.client.host
    user_agent = http_request.headers.get('user-agent')
    
    # 验证token
    token = await get_token_from_request(request)
    
    # 检查速率限制（批量提取需要更严格的限制）
    await check_rate_limits(token, client_ip)
    
    results = []
    success_count = 0
    failed_count = 0
    duplicate_count = 0
    
    # 并发提取（限制并发数）
    semaphore = asyncio.Semaphore(3)  # 最多3个并发
    
    async def extract_with_semaphore(url: str):
        async with semaphore:
            try:
                record = await extract_single_article(
                    url, token, client_ip, user_agent, request.force_refresh
                )
                
                if record.status == ExtractionStatus.SUCCESS:
                    return ExtractionResponse(
                        success=True,
                        message="提取成功",
                        data=ExtractionRecordOut.from_orm(record),
                        is_duplicate=False
                    )
                elif record.status == ExtractionStatus.DUPLICATE:
                    return ExtractionResponse(
                        success=True,
                        message="已存在（缓存）",
                        data=ExtractionRecordOut.from_orm(record),
                        is_duplicate=True
                    )
                else:
                    return ExtractionResponse(
                        success=False,
                        message="提取失败",
                        error_code="EXTRACTION_FAILED"
                    )
            except Exception as e:
                return ExtractionResponse(
                    success=False,
                    message=str(e),
                    error_code="ERROR"
                )
    
    # 执行批量提取
    tasks = [extract_with_semaphore(url) for url in request.urls]
    results = await asyncio.gather(*tasks)
    
    # 统计结果
    for result in results:
        if result.success:
            if result.is_duplicate:
                duplicate_count += 1
            else:
                success_count += 1
        else:
            failed_count += 1
    
    # 构建批量响应
    batch_response = BatchExtractionResponse(
        success=failed_count == 0,
        message=f"批量提取完成：成功{success_count}，失败{failed_count}，重复{duplicate_count}",
        total_count=len(request.urls),
        success_count=success_count,
        failed_count=failed_count,
        duplicate_count=duplicate_count,
        results=results
    )
    
    return ResultResponse[BatchExtractionResponse](result=batch_response)


@extraction_router.post(
    "/history",
    name="Get Extraction History",
    summary="获取提取历史",
    response_model=ResultResponse[ExtractionHistoryResponse]
)
async def get_extraction_history(
    request: ExtractionHistoryRequest
) -> ResultResponse[ExtractionHistoryResponse]:
    """获取用户的提取历史"""
    
    # 验证token
    token = await get_token_from_request(request)
    
    # 构建查询条件
    filters = {'access_token': token}
    if request.status is not None:
        filters['status'] = request.status
    
    # 分页查询
    offset = (request.page - 1) * request.page_size
    total = await ExtractionRecord.filter(**filters).count()
    records = await ExtractionRecord.filter(**filters)\
        .order_by('-created_at')\
        .offset(offset)\
        .limit(request.page_size)\
        .all()
    
    # 计算分页信息
    pages = (total + request.page_size - 1) // request.page_size
    
    # 转换为简化输出格式
    items = [ExtractionRecordSimple.from_orm(record) for record in records]
    
    history_response = ExtractionHistoryResponse(
        total=total,
        page=request.page,
        page_size=request.page_size,
        pages=pages,
        items=items
    )
    
    return ResultResponse[ExtractionHistoryResponse](result=history_response)


# 管理员相关接口
@extraction_router.get(
    "/admin/records",
    name="List All Extraction Records",
    summary="获取所有提取记录（管理员）",
    response_model=ResultResponse[List[ExtractionRecordAdmin]]
)
async def list_all_extraction_records(
    query: ExtractionQuery = Depends(),
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[List[ExtractionRecordAdmin]]:
    """获取所有提取记录（管理员专用）"""
    
    # 构建查询条件
    filters = {}
    if query.status is not None:
        filters['status'] = query.status
    if query.access_token_id is not None:
        filters['access_token_id'] = query.access_token_id
    if query.author is not None:
        filters['author__icontains'] = query.author
    if query.start_time is not None:
        filters['created_at__gte'] = query.start_time
    if query.end_time is not None:
        filters['created_at__lte'] = query.end_time
    
    # 分页查询
    offset = (query.page - 1) * query.page_size
    records = await ExtractionRecord.filter(**filters)\
        .order_by('-created_at')\
        .offset(offset)\
        .limit(query.page_size)\
        .all()
    
    results = [ExtractionRecordAdmin.from_orm(record) for record in records]
    return ResultResponse[List[ExtractionRecordAdmin]](result=results)


@extraction_router.get(
    "/admin/stats",
    name="Get Extraction Statistics",
    summary="获取提取统计（管理员）",
    response_model=ResultResponse[ExtractionStats]
)
async def get_extraction_statistics(
    request: ExtractionStatsRequest = Depends(),
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[ExtractionStats]:
    """获取提取统计（管理员专用）"""
    
    # 计算时间范围
    start_time = datetime.now() - timedelta(days=request.days)
    
    # 构建查询条件
    filters = {'created_at__gte': start_time}
    if request.access_token_id is not None:
        filters['access_token_id'] = request.access_token_id
    
    # 获取所有相关记录
    records = await ExtractionRecord.filter(**filters).all()
    
    # 统计数据
    total_extractions = len(records)
    success_extractions = len([r for r in records if r.status == ExtractionStatus.SUCCESS])
    failed_extractions = len([r for r in records if r.status == ExtractionStatus.FAILED])
    duplicate_extractions = len([r for r in records if r.status == ExtractionStatus.DUPLICATE])
    
    # 计算成功率
    success_rate = (success_extractions / total_extractions * 100) if total_extractions > 0 else 0
    
    # 计算平均响应时间
    response_times = [r.core_response_time for r in records if r.core_response_time is not None]
    average_response_time = sum(response_times) / len(response_times) if response_times else 0
    
    # 计算唯一文章数
    unique_articles = len(set(r.url_hash for r in records if r.status == ExtractionStatus.SUCCESS))
    
    # 统计热门作者
    author_count = {}
    for record in records:
        if record.author and record.status == ExtractionStatus.SUCCESS:
            author_count[record.author] = author_count.get(record.author, 0) + 1
    
    top_authors = sorted(author_count.items(), key=lambda x: x[1], reverse=True)[:10]
    
    # 按天统计
    daily_stats = {}
    for record in records:
        date_key = record.created_at.strftime('%Y-%m-%d')
        if date_key not in daily_stats:
            daily_stats[date_key] = {'total': 0, 'success': 0, 'failed': 0, 'duplicate': 0}
        
        daily_stats[date_key]['total'] += 1
        if record.status == ExtractionStatus.SUCCESS:
            daily_stats[date_key]['success'] += 1
        elif record.status == ExtractionStatus.FAILED:
            daily_stats[date_key]['failed'] += 1
        elif record.status == ExtractionStatus.DUPLICATE:
            daily_stats[date_key]['duplicate'] += 1
    
    stats = ExtractionStats(
        total_extractions=total_extractions,
        success_extractions=success_extractions,
        failed_extractions=failed_extractions,
        duplicate_extractions=duplicate_extractions,
        success_rate=success_rate,
        average_response_time=average_response_time,
        unique_articles=unique_articles,
        top_authors=top_authors,
        daily_stats=daily_stats
    )
    
    return ResultResponse[ExtractionStats](result=stats)
