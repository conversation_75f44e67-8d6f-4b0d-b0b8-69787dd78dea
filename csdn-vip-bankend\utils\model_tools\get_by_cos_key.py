# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/6/15 11:47
# @File     : get_by_cos_key.py
# @Project  : template
from config import settings
from core.sdk.tencent_cloud import get_cos


def get_cos_key_url(cos_key: str):
    key = cos_key
    client = get_cos()
    url = client.get_presigned_url(
        Method='GET',
        Bucket=settings.TENCENT.COS.BUCKET,
        Key=key,
        Expired=60 * 60  # 过期时间请根据自身场景定义
    )
    print(url)
    return url