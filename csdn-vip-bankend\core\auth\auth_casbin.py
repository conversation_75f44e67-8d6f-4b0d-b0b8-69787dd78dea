import casbin
import re
import casbin_tortoise_adapter
from fastapi import Request, Depends
from typing import Any
from models import AdminUser
from config import settings
from core.exce.base import AuthenticationError
from utils.base import Singleton
from .base import get_current_active_user


class TortoiseCasbin(metaclass=Singleton):
    def __init__(self, model: str) -> None:
        adapter = casbin_tortoise_adapter.TortoiseAdapter()

        # casbin 的方法，想要查看更多方法，点进 Enforcer 里面看
        self.enforce = casbin.Enforcer(str(model), adapter)

    async def has_permission(self, user: str, obj: str, act: str) -> bool:
        """
        判断是否拥有权限
        """
        return self.enforce.enforce(user, obj, act)

    async def add_permission_for_role(self, role: str, obj: str, act: str):
        """
        添加角色权限
        """
        return await self.enforce.add_policy(role, obj, act)

    async def get_permissions_for_user_or_role(self, user_or_role: str):
        """
        gets permissions for a user or role.
        """
        return await self.enforce.get_filtered_policy(0, user_or_role)

    async def remove_permission(self, sub: str, obj: str, act: str):
        return await self.enforce.remove_policy(sub, obj, act)

    async def delete_roles_for_user(self, user: str):
        """
        deletes all roles for a user.
        Returns false if the user does not have any roles (aka not affected).
        """
        return await self.enforce.delete_roles_for_user(user)

    def __getattr__(self, attr: str) -> Any:
        return getattr(self.enforce, attr)


async def check_authority(policy):
    """
    进行权限认证
    :param policy: 字符串，以 user,obj,act拼接而成，例如"user,auth,add"
    :return:
    """
    user, obj, act = policy.split(',')
    e = await get_casbin()
    if not await e.has_permission(user, obj, act):
        raise AuthenticationError(err_desc=f'Permission denied: [{policy}]')


async def get_casbin() -> TortoiseCasbin:
    """
    获取 casbin 权限认证对象，初始化时要加载一次权限模型信息
    :return:
    """
    tor_casbin = TortoiseCasbin(settings.CASBIN.CASBIN_MODEL_PATH)
    if not hasattr(tor_casbin, 'load'):
        setattr(tor_casbin, 'load', True)
        await tor_casbin.load_policy()
    return tor_casbin


async def get_casbin_rule_user(
        request: Request,
        current_user: AdminUser = Depends(get_current_active_user),
) -> AdminUser:
    if not current_user.is_super:
        e = await get_casbin()
        obj = re.sub(r'api/.*?/', '', request.url.path)
        if not await e.has_permission(current_user.username, obj, request.method):
            raise AuthenticationError("无访问权限")
    return current_user
