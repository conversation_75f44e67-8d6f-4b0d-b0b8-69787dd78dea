from typing import Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from models import ExtractionCore, CoreStatus
from utils.model_pydantic import pydantic_model_creator


# 输出模型（排除敏感信息）
ExtractionCoreOut = pydantic_model_creator(
    ExtractionCore,
    name="ExtractionCoreOut",
    exclude=(
        'is_deleted',
        'auth_token',  # 敏感信息，不对外暴露
    )
)

# 管理员查看的完整信息
ExtractionCoreAdmin = pydantic_model_creator(
    ExtractionCore,
    name="ExtractionCoreAdmin",
    exclude=(
        'is_deleted',
    )
)

# 简化的输出模型（仅状态信息）
ExtractionCoreStatus = pydantic_model_creator(
    ExtractionCore,
    name="ExtractionCoreStatus",
    exclude=(
        'is_deleted',
        'auth_token',
        'description',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    )
)


class ExtractionCoreCreate(BaseModel):
    """创建提取核心配置"""
    name: str = Field(..., description="核心服务名称")
    description: Optional[str] = Field(None, description="服务描述")
    api_endpoint: str = Field(..., description="API端点URL")
    auth_token: str = Field(..., description="认证令牌")
    timeout_seconds: int = Field(30, description="请求超时时间（秒）")
    max_retries: int = Field(3, description="最大重试次数")
    priority: int = Field(1, description="优先级（数字越小优先级越高）")
    weight: int = Field(1, description="负载均衡权重")
    max_concurrent_requests: int = Field(10, description="最大并发请求数")
    health_check_url: Optional[str] = Field(None, description="健康检查URL")

    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('服务名称不能为空')
        return v.strip()

    @validator('api_endpoint')
    def validate_api_endpoint(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('API端点不能为空')
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API端点必须以http://或https://开头')
        return v.strip()

    @validator('auth_token')
    def validate_auth_token(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('认证令牌不能为空')
        return v.strip()

    @validator('timeout_seconds')
    def validate_timeout_seconds(cls, v):
        if v <= 0 or v > 300:
            raise ValueError('超时时间必须在1-300秒之间')
        return v

    @validator('max_retries')
    def validate_max_retries(cls, v):
        if v < 0 or v > 10:
            raise ValueError('最大重试次数必须在0-10之间')
        return v

    @validator('priority')
    def validate_priority(cls, v):
        if v <= 0:
            raise ValueError('优先级必须大于0')
        return v

    @validator('weight')
    def validate_weight(cls, v):
        if v <= 0:
            raise ValueError('权重必须大于0')
        return v

    @validator('max_concurrent_requests')
    def validate_max_concurrent_requests(cls, v):
        if v <= 0 or v > 1000:
            raise ValueError('最大并发请求数必须在1-1000之间')
        return v

    class Config:
        schema_extra = {
            'example': {
                'name': '核心服务1',
                'description': 'CSDN文章提取核心服务',
                'api_endpoint': 'https://api.example.com/extract',
                'auth_token': 'your_auth_token_here',
                'timeout_seconds': 30,
                'max_retries': 3,
                'priority': 1,
                'weight': 1,
                'max_concurrent_requests': 10,
                'health_check_url': 'https://api.example.com/health'
            }
        }


class ExtractionCoreUpdate(BaseModel):
    """更新提取核心配置"""
    name: Optional[str] = Field(None, description="核心服务名称")
    description: Optional[str] = Field(None, description="服务描述")
    api_endpoint: Optional[str] = Field(None, description="API端点URL")
    auth_token: Optional[str] = Field(None, description="认证令牌")
    status: Optional[CoreStatus] = Field(None, description="服务状态")
    timeout_seconds: Optional[int] = Field(None, description="请求超时时间（秒）")
    max_retries: Optional[int] = Field(None, description="最大重试次数")
    priority: Optional[int] = Field(None, description="优先级")
    weight: Optional[int] = Field(None, description="负载均衡权重")
    max_concurrent_requests: Optional[int] = Field(None, description="最大并发请求数")
    health_check_url: Optional[str] = Field(None, description="健康检查URL")

    @validator('api_endpoint')
    def validate_api_endpoint(cls, v):
        if v is not None:
            if not v.startswith(('http://', 'https://')):
                raise ValueError('API端点必须以http://或https://开头')
        return v

    @validator('timeout_seconds')
    def validate_timeout_seconds(cls, v):
        if v is not None and (v <= 0 or v > 300):
            raise ValueError('超时时间必须在1-300秒之间')
        return v

    @validator('max_retries')
    def validate_max_retries(cls, v):
        if v is not None and (v < 0 or v > 10):
            raise ValueError('最大重试次数必须在0-10之间')
        return v

    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and v <= 0:
            raise ValueError('优先级必须大于0')
        return v

    @validator('weight')
    def validate_weight(cls, v):
        if v is not None and v <= 0:
            raise ValueError('权重必须大于0')
        return v

    @validator('max_concurrent_requests')
    def validate_max_concurrent_requests(cls, v):
        if v is not None and (v <= 0 or v > 1000):
            raise ValueError('最大并发请求数必须在1-1000之间')
        return v


class ExtractionCoreQuery(BaseModel):
    """查询提取核心配置"""
    status: Optional[CoreStatus] = Field(None, description="服务状态")
    is_healthy: Optional[bool] = Field(None, description="是否健康")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")


class ExtractionCoreStats(BaseModel):
    """提取核心统计信息"""
    total_requests: int = Field(..., description="总请求次数")
    success_requests: int = Field(..., description="成功请求次数")
    failed_requests: int = Field(..., description="失败请求次数")
    success_rate: float = Field(..., description="成功率")
    average_response_time: float = Field(..., description="平均响应时间")
    current_requests: int = Field(..., description="当前请求数")
    max_concurrent_requests: int = Field(..., description="最大并发数")
    utilization_rate: float = Field(..., description="利用率")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")
    is_healthy: bool = Field(..., description="是否健康")


class HealthCheckRequest(BaseModel):
    """健康检查请求"""
    core_id: int = Field(..., description="核心服务ID")
    timeout: int = Field(10, description="检查超时时间")


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    core_id: int = Field(..., description="核心服务ID")
    is_healthy: bool = Field(..., description="是否健康")
    response_time: float = Field(..., description="响应时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    check_time: datetime = Field(..., description="检查时间")


class LoadBalancingInfo(BaseModel):
    """负载均衡信息"""
    available_cores: int = Field(..., description="可用核心数")
    total_cores: int = Field(..., description="总核心数")
    total_current_requests: int = Field(..., description="总当前请求数")
    total_capacity: int = Field(..., description="总容量")
    utilization_rate: float = Field(..., description="整体利用率")
    cores_status: list[ExtractionCoreStats] = Field(..., description="各核心状态")


class CorePerformanceMetrics(BaseModel):
    """核心性能指标"""
    core_id: int = Field(..., description="核心服务ID")
    period_start: datetime = Field(..., description="统计开始时间")
    period_end: datetime = Field(..., description="统计结束时间")
    total_requests: int = Field(..., description="总请求数")
    success_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    average_response_time: float = Field(..., description="平均响应时间")
    min_response_time: float = Field(..., description="最小响应时间")
    max_response_time: float = Field(..., description="最大响应时间")
    throughput: float = Field(..., description="吞吐量（请求/秒）")
    error_rate: float = Field(..., description="错误率")
    availability: float = Field(..., description="可用性")
