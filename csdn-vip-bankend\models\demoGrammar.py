from .base import BaseModel, fields
from datetime import datetime
from tortoise import models

# 所有字段类型
"""
"BigIntField",
"BinaryField",
"BooleanField",
"CharEnumField",
"Char<PERSON>ield",
"DateField",
"DatetimeField",
"FloatField",
"IntEnumField",
"IntField",
"JSONField",
"SmallIntField",
"TextField",
"TimeDeltaField",
"UUIDField",
"""


class Teacher(BaseModel):
    """老师"""
    name = fields.CharField(max_length=128, null=False, description="姓名")  # 定义字符类型
    age = fields.IntField(null=True, description="性别")  # 定义数字类型
    info = fields.JSONField(default=None, null=True, description="表单设计")  # 用于接收 list 或 字典数据
    books: fields.ReverseRelation["Books"]  # 反向关联的标志，让看到Teacher模型时，知道他被其他表关联了

    class Meta:
        table = "teacher"

    class PydanticMeta:
        exclude = ["is_deleted"]


class Student(BaseModel):
    """学生"""
    name = fields.CharField(max_length=128, null=False, description="姓名")  # 定义字符类型
    age = fields.IntField(null=True, description="性别")  # 定义数字类型
    info = fields.JSONField(default=None, null=True, description="表单设计")  # 用于接收list 或 字典数据
    books: fields.ReverseRelation["Books"]

    class Meta:
        table = "student"

    class PydanticMeta:
        exclude = ["is_deleted"]


class BooksStore(BaseModel):
    """卖书籍的商店"""
    store_name = fields.CharField(max_length=128, null=False, description="书在哪家店")
    money = fields.FloatField(max_length=128, null=True, description="书在商店的价格")
    # initiator = fields.ManyToManyField(        # 多对多的写法
    #     'template.Student',
    #     null=True,
    #     related_name='student_books',  # 返回时的数据关联数据
    #     description="学生与书的中间表",
    #     # on_delete='CASCADE',  # 是否关联删除
    #     required=True,
    #     through='role_process_relation',  # 中间表的名称
    # )
    book: fields.ManyToManyRelation["Books"] = fields.ManyToManyField(
        "template.Books", related_name="book_na", through="book_and_store"
    )
    is_chain_store = fields.BooleanField(default=False, description="是否连锁店")
    store_codenumber = fields.CharField(max_length=128, description="商店编码")
    store_address = fields.CharField(max_length=128, description="商店精确地址（不能重复）")
    changed_time = fields.DatetimeField(default=datetime.now(), description="商店信息刷新时间")
    store_info = fields.TextField(default="", null=True, description="商店简介")

    class Meta:
        table = "books_store"

        # 这个联合唯一约束可以确保在数据库中，每个不同的dps_table_id和project_id "组合"只能出现一次
        unique_together = (("store_codenumber", "store_address"),)

    class PydanticMeta:
        exclude = ["is_deleted"]


class Books(BaseModel):
    """书籍"""
    book_name = fields.CharField(max_length=128, null=False, description="书名称")
    book_stu = fields.ForeignKeyField(  # book_stu正向关联时会显示的名称
        "template.Student",
        related_name="books",  # 反向关联时显示的名称；即查询students时关联的数据的名称
        null=True,
        on_delete=fields.CASCADE,  # 关联删除
        description="被学生借走",
    )
    books_tech: fields.ForeignKeyRelation[Teacher] = fields.ForeignKeyField(  # 这种写法会在关联的另一张表里有反向关联的写法
        "template.Teacher",
        default=None,
        null=True,
        on_delete=fields.CASCADE,  # 关联删除
        related_name="books",  # 反向关联时显示的关联名称
        description="被老师借走",
    )
    bookstore: fields.ManyToManyRelation[BooksStore]

    class Meta:
        table = "books"

    class PydanticMeta:
        exclude = ["is_deleted"]


class BooksVersion(BaseModel):
    """书籍版本及其信息"""
    comp_name = fields.CharField(max_length=128, null=False, description="厂家")
    money = fields.FloatField(max_length=128, null=True, description="价格")

    class Meta:
        table = "books_version"

    class PydanticMeta:
        exclude = ["is_deleted"]


# class DashboardSlice(models.Model):  ## 如果继承的是tortoise的最基础模型models，则自己耀手动确定主键
#     id = fields.IntField(pk=True)
#     dashboard = fields.ForeignKeyField('bi.Dashboard', on_delete='CASCADE', related_name='slices')
#     slice = fields.ForeignKeyField('bi.Slice', on_delete='CASCADE', related_name='dashboards')
#
#     class Meta:
#         table = 'dashboard_slices'
#         unique_together = (('dashboard_id', 'slice_id'),)
