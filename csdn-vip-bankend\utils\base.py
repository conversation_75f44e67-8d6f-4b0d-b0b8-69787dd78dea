import threading
import uuid
import random
from passlib.context import Crypt<PERSON><PERSON>xt
from tortoise import Tortoise
from typing import List, Type, Union
from tortoise.contrib.pydantic import PydanticModel
from tortoise.models import Model
from pydantic import ValidationError

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Singleton(type):
    _instance_lock = threading.Lock()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def __call__(cls, *args, **kwargs):
        with cls._instance_lock:
            if not hasattr(cls, '_instance'):
                cls._instance = super().__call__(*args, **kwargs)
        return cls._instance


def gen_uuid() -> str:
    return uuid.uuid1().hex


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    :param plain_password: 原密码
    :param hashed_password: hash后的密码
    :return:
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    获取 hash 后的密码
    :param password:
    :return:
    """
    return pwd_context.hash(password)


async def start_db_up(TORTOISE_ORM):
    await Tortoise.init(config=TORTOISE_ORM)
    await Tortoise.generate_schemas()


def generate_random_str(random_length: int = 16) -> str:
    """
    生成一个指定长度的随机字符串
    """
    random_str = ''
    base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(random_length):
        random_str += base_str[random.randint(0, length)]
    return random_str


def get_fetch_fields(
        pydantic_class: "Type[PydanticModel]", model_class: "Type[Model]"
) -> List[str]:
    """
    Recursively collect fields needed to fetch
    :param pydantic_class: The pydantic model class
    :param model_class: The tortoise model class
    :return: The list of fields to be fetched
    """
    fetch_fields = []
    for field_name, field_type in pydantic_class.__annotations__.items():
        origin = getattr(field_type, "__origin__", None)
        if origin in (list, List, Union):
            field_type = field_type.__args__[0]

        # noinspection PyProtectedMember
        if field_name in model_class._meta.fetch_fields and issubclass(field_type, PydanticModel):
            fetch_fields.append(field_name)
    return fetch_fields


def ignore_none(**args):
    """
    去除None的查询字段
    @param args:
    @return:
    """
    return {key: value for key, value in args.items() if value is not None}


class PasswordTool:
    """
      密码工具类
    """

    def __init__(self, password):
        self.password = password
        self.strength_level = 0

    def check_number_exist(self):
        """
          判断是否含数字
        """
        has_number = False
        for c in self.password:
            if c.isnumeric():
                has_number = True
                break
        return has_number

    def check_letter_exist(self):
        """
          判断是否含字母
        """
        has_both_letter = False
        has_upper_letter = False
        has_lower_letter = False
        for c in self.password:
            if c.isupper():
                has_upper_letter = True
            elif c.islower():
                has_lower_letter = True
            has_both_letter = has_upper_letter and has_lower_letter
            if has_both_letter:
                break
        return has_both_letter

    def check_specialchar_exist(self):
        """
          判断是否包含特殊字符
        """
        has_specialchar = False
        specialchar_list = ['+', '-', '*', '/', '_', '&', '%', ',']
        for c in self.password:
            if c in specialchar_list:
                has_specialchar = True
                break
        return has_specialchar

    def process_password(self):
        """
          判断是否符合规则
        """
        # 规则1：长度至少6位
        if 6 <= len(self.password) <= 26:
            self.strength_level += 1
        else:
            raise ValidationError('密码长度至少6位')

        # 规则2：必须包含数字
        if self.check_number_exist():
            self.strength_level += 1
        else:
            raise ValidationError('密码需要包含数字')

        # 规则3：必须包含大小写字母
        if self.check_letter_exist():
            self.strength_level += 1
        else:
            raise ValidationError('密码需要包含大小写字母')


if __name__ == '__main__':
    print(uuid.uuid1().hex)
