from typing import cast, List
from fastapi import Depends, APIRouter
from core.auth.auth_casbin import get_casbin
from core.auth.base import get_current_active_user
from core.exce.response_code import HttpStatus
from core.response import ResultResponse, ListResult
from models import (
    AdminObject, AdminAction, AdminRole, AdminUser
)
from schemas import (
    AdminObjectOut, AdminObjectCreateAndUpdate,
    AdminActionOut, AdminActionCreateAndUpdate,
    AdminRoleOut, AdminRoleCreateBase, AdminRoleUpdate,
    PolicyOut, RolePolicyOutQuery,
    RolePolicyCreate, PolicyDelQuery, UserPolicyOutQuery, AdminUserPolicyCreate, UserRoleOutQuery, UserRoleOut,
    UserRoleCreate, UserRoleDelQuery, PolicyTestQuery, AdminUserOut
)
from schemas.casbin import RolePolicyListCreate, UsersInAdminRoleOut, PolicyWithTypeOut, AdminCasbinObjectQueryByName
from utils.base import ignore_none
from utils.fastapi_crudrouter import TortoiseCRUDRouter
from utils.fastapi_crudrouter.core import NOT_FOUND

admin_casbin_object_router = TortoiseCRUDRouter(
    db_model=AdminObject,
    list_schema=AdminObjectOut,
    create_schema=AdminObjectCreateAndUpdate,
    update_schema=AdminObjectCreateAndUpdate,
    prefix="/admin-casbin-object",
    tags=["admin casbin目标(访问资源)"],
    get_all_route=False,
    get_one_route=[Depends(get_current_active_user)],
    create_route=False,
    update_route=False,
    delete_one_route=[Depends(get_current_active_user)],
)

@admin_casbin_object_router.get (
    "",
    name="Get All",
    summary="Get All",
    response_model=ResultResponse[ListResult[AdminObjectOut]],
    dependencies=[Depends(get_current_active_user)]
)
async def get_admin_casbin_object(query_params: AdminCasbinObjectQueryByName = Depends()) -> \
        ResultResponse[ListResult[AdminObjectOut]]:
    skip, limit, name, obj, type = query_params.skip, query_params.limit, query_params.name, query_params.obj, query_params.type
    query = AdminObject.filter(is_deleted=False, **ignore_none(
        name__icontains=name, obj__icontains=obj, type__icontains=type
    )).all()
    all_count = await query.count()
    query = query.all().order_by('id').offset(cast(int, skip))
    if limit:
        query = query.limit(limit)
    data = await AdminObjectOut.from_queryset(query)
    return ResultResponse[ListResult[AdminObjectOut]](
        result=ListResult[AdminObjectOut](
            all_count=all_count,
            data=data
        )
    )





@admin_casbin_object_router.post(
    "",
    name="create one",
    summary="create one",
    response_model=ResultResponse[AdminObjectOut],

)
async def create_admin_casbin_object(admin_object: AdminObjectCreateAndUpdate) -> ResultResponse[AdminObjectOut]:
    admin_object_orm = await AdminObject.create(**admin_object.dict(exclude={"acts", }))
    for act_id in admin_object.acts:
        admin_action = await AdminAction.get_or_none(id=act_id)
        if admin_action:
            await admin_object_orm.acts.add(admin_action)
    data = await AdminObjectOut.from_tortoise_orm(admin_object_orm)
    return ResultResponse[AdminObjectOut](result=data)


@admin_casbin_object_router.put(
    "/{item_id}",
    name="update one",
    summary="update one",
    response_model=ResultResponse[AdminObjectOut],

)
async def update_admin_casbin_object(
        item_id: int, admin_object: AdminObjectCreateAndUpdate
) -> ResultResponse[AdminObjectOut]:
    result = await AdminObject.filter(id=item_id).update(**admin_object.dict(exclude={"acts", }))
    if result == 0:
        raise NOT_FOUND
    admin_object_orm = await AdminObject.get(id=item_id)
    for act_id in admin_object.acts:
        admin_action = await AdminAction.get_or_none(id=act_id)
        if admin_action:
            await admin_object_orm.acts.add(admin_action)
    data = await AdminObjectOut.from_tortoise_orm(admin_object_orm)
    return ResultResponse[AdminObjectOut](result=data)


admin_casbin_action_router = TortoiseCRUDRouter(
    db_model=AdminAction,
    list_schema=AdminActionOut,
    create_schema=AdminActionCreateAndUpdate,
    update_schema=AdminActionCreateAndUpdate,
    prefix="/admin-casbin-action",
    tags=["admin casbin动作(访问方式)"],
    get_all_route=[Depends(get_current_active_user)],
    get_one_route=[Depends(get_current_active_user)],
    create_route=[Depends(get_current_active_user)],
    update_route=[Depends(get_current_active_user)],
    delete_one_route=[Depends(get_current_active_user)],
)

admin_role_router = TortoiseCRUDRouter(
    db_model=AdminRole,
    list_schema=AdminRoleOut,
    create_schema=AdminRoleCreateBase,
    update_schema=AdminRoleUpdate,
    prefix="/admin-role",
    tags=["admin角色"],
    get_all_route=[Depends(get_current_active_user)],
    get_one_route=[Depends(get_current_active_user)],
    create_route=False,
    update_route=[Depends(get_current_active_user)],
    delete_one_route=[Depends(get_current_active_user)],
)
# 角色里通过成员的id指定成员中的老大
@admin_role_router.put(
    '-biggest/{item_id}',
    name="Point user biggest id",
    summary="Point user biggest id",
    response_model=ResultResponse[str],
    dependencies=[Depends(get_current_active_user)]
)
async def point_user_biggist_id(
        item_id: int,
        user_biggest_id: int,
        current_user: AdminUser = Depends(get_current_active_user),
) -> ResultResponse[str]:
    # 拿到当前角色的父级角色，看当前登录用户是否是超级管理员，或者是否是父级角色里的人
    if current_user.is_super:
        await AdminRole.filter(id=item_id).update(role_admin_id=user_biggest_id)
        return ResultResponse[str](result="成员老大指定成功")
    else:
        now_role_orm = await AdminRole.filter(id=item_id).values()
        p_role_id = now_role_orm.get("parent_role_id")
        p_role_orm = await AdminRole.filter(id=p_role_id).values()
        p_role_role = p_role_orm.get("role")
        current_user_name = current_user.username  # 获得当前用户的名字
        e = await get_casbin()   # 查找casbin中的数据
        roles = e.get_roles_for_user(current_user_name)  # 查找角色中所有成员
        if p_role_role in roles:
            await AdminRole.filter(id=item_id).update(role_admin_id=user_biggest_id)
            return ResultResponse[str](result="成员老大指定成功")
        else:
            return ResultResponse[str](code=401, message="没有权限修改指定角色管理员")


@admin_role_router.get(
    "",
    name="Get By Name Or Role",
    summary="Get By Name Or Role",
    response_model=ResultResponse[ListResult[AdminRoleOut]],
    dependencies=[Depends(get_current_active_user)]
)
async def get_by_name_or_role(query_params: AdminCasbinObjectQueryByName = Depends()) ->ResultResponse[ListResult[AdminRoleOut]]:

    skip, limit, name, role = query_params.skip, query_params.limit, query_params.name, query_params.role
    query = AdminRole.filter(is_deleted=False, **ignore_none(
        name__icontains=name, role__icontains=role
    )).all()

    all_count = await query.count()
    query = query.all().order_by('name').offset(cast(int, skip))
    if limit:
        query = query.limit(limit)
    data = await AdminRoleOut.from_queryset(query)
    return ResultResponse[ListResult[AdminRoleOut]](
        result=ListResult[AdminRoleOut](
            all_count=all_count,
            data=data
        )
    )









@admin_role_router.get(
    "-children",
    name="get role children",
    summary="get role children",
    response_model=ResultResponse[ListResult[AdminRoleOut]],
    dependencies=[Depends(get_current_active_user)]
)
async def get_all_children_roles(current_user: AdminUser = Depends(get_current_active_user)) -> ResultResponse[
    ListResult[AdminRoleOut]]:
    """递归获取角色下所有的子角色"""

    if not current_user.is_super:
        username = current_user.username
        e = await get_casbin()
        roles = await e.get_roles_for_user(username)
        all_roles_id = []
        # print(roles)
        if roles:
            for role in roles:
                role_id = await AdminRole.filter(role=role).values_list("id", flat=True)
                # print(role_id)
                results = await get_all_children_roles_recursive(role_id[0])
                all_roles_id = all_roles_id + results
                # results = await get_all_children_roles_recursive(role)
        # print(all_roles_id)
        all_roles = AdminRole.filter(id__in=all_roles_id).order_by("id")
    else:
        all_roles = AdminRole.all().order_by("id")
    all_count = await all_roles.count()
    data = await AdminRoleOut.from_queryset(all_roles)

    return ResultResponse[ListResult[AdminRoleOut]](
        result=ListResult[AdminRoleOut](
            all_count=all_count,
            data=data
        )
    )


async def get_all_children_roles_recursive(role_id):
    """递归获取角色下所有的子角色"""
    results = []
    # role = await AdminRole.filter(id=role_id).first().values()
    role = await AdminRole.filter(id=role_id).first()
    if role:
        results.append(role_id)
        await role.fetch_related('children_role')
        admin_role = await AdminRoleOut.from_tortoise_orm(role)
        admin_role_dict = admin_role.dict()
        children = admin_role_dict.get("children_role")
        # print(admin_role_dict)
        # child = role.get("children_role")
        # print(child)
        if children:
            for child in children:
                results.extend(await get_all_children_roles_recursive(child.get("id")))
    return results


@admin_role_router.post(
    "",
    name="Create One",
    summary="Create One",
    response_model=ResultResponse[str],
    dependencies=[Depends(get_current_active_user)]
)
async def create_admin_role(role_info: AdminRoleCreateBase,
                            current_user: AdminUser = Depends(get_current_active_user)) -> ResultResponse[str]:
    username = current_user.username
    e = await get_casbin()
    roles = await e.get_roles_for_user(username)
    # 创建角色首先前端需要获取当前用户属于的所有角色，然后在创建的时候选择一个角色作为父角色
    # 检测前端传来的角色id是否属于当前用户所有的角色里面
    role_info = role_info.dict()
    role_info.update({"create_by": username})
    p_role_id = role_info.get("parent_role_id")
    p_role_info = await AdminRole.get_or_none(id=p_role_id)
    if not p_role_info:
        return ResultResponse[str](code=HttpStatus.HTTP_404, message='选择的父角色不存在')
    if not current_user.is_super and p_role_info.role not in roles:
        return ResultResponse[str](code=401, message='当前创建人不是超级管理员且不属于选择的父角色')
    # 如果在选择的父角色下面创建的角色，那么创建人是当前用户，创建的子角色的父角色就是选择的父角色
    admin_role = await AdminRole.create(**role_info)
    print(admin_role)
    # role_resource_orm = await AdminRoleResource.create(admin_role_id=admin_role.id)
    print("98765432")
    return ResultResponse[str](result="角色创建成功")


@admin_role_router.get(
    "-users/{item_id}",
    name="get one role users",
    summary="get one role users",
    response_model=ResultResponse[UsersInAdminRoleOut],
)
async def get_users_in_role(item_id: int) -> ResultResponse[UsersInAdminRoleOut]:
    admin_role_orm = await AdminRole.get_or_none(id=item_id)
    e = await get_casbin()
    results = await e.get_users_for_role(admin_role_orm.role)
    admin_users = []
    for result in results:
        admin_user_orm = await AdminUser.filter(username=result).values()
        admin_users.append(admin_user_orm[0])
    admin_role = await AdminRole.filter(id=item_id).values()
    admin_role_dict = admin_role[0]
    admin_role_dict.pop("is_deleted")
    admin_role_dict.update({"users": admin_users})
    users_in_role = UsersInAdminRoleOut.parse_obj(admin_role_dict)
    return ResultResponse[UsersInAdminRoleOut](result=users_in_role)


admin_casbin_router = APIRouter(
    prefix="/admin-casbin-policy",
    tags=["admin casbin 策略"],
)


@admin_casbin_router.get(
    '-admin-user',
    summary='get user policy',
    description='get user policy',
    response_model=ResultResponse[ListResult[PolicyOut]]
)
async def get_admin_user_policy(
        user_policy: UserPolicyOutQuery = Depends()
) -> ResultResponse:
    admin_user_orm = await AdminUser.get_or_none(id=user_policy.user_id)
    if not admin_user_orm:
        return ResultResponse[str](code=HttpStatus.HTTP_404, message='用户不存在')
    e = await get_casbin()
    results = await e.get_permissions_for_user_or_role(admin_user_orm.username)
    return ResultResponse[ListResult[PolicyOut]](
        result=ListResult[PolicyOut](
            data=[PolicyOut(sub=result[0], obj=result[1], act=result[2]) for result in results],
            all_count=len(results)
        ),
    )


@admin_casbin_router.post(
    "-admin-user",
    summary="add user policy",
    description="add user policy",
    response_model=ResultResponse,
)
async def add_admin_user_policy(admin_user_policy: AdminUserPolicyCreate, current_user: AdminUser = Depends(
    get_current_active_user)) -> ResultResponse:
    is_super = current_user.is_super
    username = current_user.username
    # 查看角色是否存在，如果不存在报错吗，如果存在继续
    admin_user_orm = await AdminUser.filter(is_deleted=False, id=admin_user_policy.admin_user_id).first().values()
    if not admin_user_orm:
        return ResultResponse(code=404, message='用户不存在')
    role_role = admin_user_orm.get('username')

    all_policy = []
    # 加载 casbin
    e = await get_casbin()
    # 查看当前用户所在角色的权限，再拿当前提交的权限对比，如果存在则创建，如果
    user_roles = await e.get_roles_for_user(username)
    for user_role in user_roles:
        user_role_policy = await e.get_permissions_for_user_or_role(user_role)
        all_policy = all_policy + user_role_policy

    # 查看当前用户权限
    nowuser_policy = await e.get_permissions_for_user_or_role(username)  # [[],[],[]]
    all_policy = all_policy + nowuser_policy

    # 去重all_policy
    # all_policy = list(set(all_policy))

    # 提交的需要创建的策略
    submit_policy = []
    for obj_act in admin_user_policy.obj_acts:
        submit_policy.append([role_role, obj_act.obj, obj_act.act])

    fail = []
    # 比较all_policy和submit_policy，如果all_policy包含submit_policy则创建，否则报错
    for sp in submit_policy:
        if is_super:
            res = await e.add_policy(sp[0], sp[1], sp[2])
            if not res:
                fail.append(sp)
        else:
            if sp not in all_policy:
                return ResultResponse(code=401,
                                      message='用户权限不足,不能给用户增加高于自身的权限')
            else:
                res = await e.add_policy(sp[0], sp[1], sp[2])
                if not res:
                    fail.append(sp)

    # 如果有失败的策略，返回部分策略已存在
    if fail:
        return ResultResponse[List](message='部分策略已存在', result=fail)
    else:
        return ResultResponse[str](message='添加策略成功')


@admin_casbin_router.get(
    '-admin-role',
    summary='get role policy',
    description='get role policy',
    response_model=ResultResponse[ListResult[PolicyWithTypeOut]]
)
async def get_role_policy(
        role_policy: RolePolicyOutQuery = Depends()
) -> ResultResponse:
    role_orm = await AdminRole.get_or_none(id=role_policy.role_id)
    if not role_orm:
        return ResultResponse[str](code=404, message='角色不存在')
    e = await get_casbin()
    results = await e.get_permissions_for_user_or_role(role_orm.role)
    print(results)
    results_with_type = []
    for result in results:
        # obj_orm = await AdminObject.get_or_none(obj=result[1])
        objs_orm = await AdminObject.filter(obj=result[1]).values()
        print(objs_orm)
        for obj_orm in objs_orm:
            r = result.copy()
            r.append(obj_orm.get('type'))
            results_with_type.append(r)
    print(results_with_type)
    return ResultResponse[ListResult[PolicyWithTypeOut]](
        result=ListResult[PolicyWithTypeOut](
            data=[PolicyWithTypeOut(sub=rt[0], obj=rt[1], act=rt[2], type=rt[3]) for rt in results_with_type],
            all_count=len(results)
        ),
    )


@admin_casbin_router.post(
    "-admin-role",
    summary="add role policy",
    description="add role policy",
    response_model=ResultResponse,
    dependencies=[Depends(get_current_active_user)]
)
async def add_role_policy(role_policy: RolePolicyCreate, current_user: AdminUser = Depends(
    get_current_active_user)) -> ResultResponse:
    is_super = current_user.is_super
    username = current_user.username
    # 查看角色是否存在，如果不存在报错吗，如果存在继续
    role_orm = await AdminRole.filter(is_deleted=False, id=role_policy.role_id).first().values()
    """
    [{'id': 17, 'create_time': datetime.datetime(2023, 5, 26, 18, 28, 15, 21226, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'update_time': datetime.datetime(2023, 5, 26, 18, 28, 15, 21226, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'create_by': 'admin', 'is_deleted': False, 'remark': None, 'name': 'admin_child_role10', 'role': 'admin_child_role10', 'parent_role_id': 1}]
    """
    if not role_orm:
        return ResultResponse(code=404, message='角色不存在')
    role_role = role_orm.get('role')

    # 查看父级角色是否存在，如果不存在报错，如果存在继续
    p_role_id = role_orm.get('parent_role_id')
    p_role_orm = await AdminRole.filter(is_deleted=False, id=p_role_id).first().values()
    """
    [{'id': 1, 'create_time': datetime.datetime(2023, 5, 20, 19, 28, 52, 52625, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'update_time': datetime.datetime(2023, 5, 20, 19, 28, 52, 52625, tzinfo=<DstTzInfo 'Asia/Shanghai' CST+8:00:00 STD>), 'create_by': 'system', 'is_deleted': False, 'remark': '系统默认创建超级管理员角色', 'name': '超级管理员', 'role': 'super_role', 'parent_role_id': None}]
    """
    if not p_role_orm:
        return ResultResponse(code=404, message='角色不存在')
    p_role_role = p_role_orm.get('role')

    all_policy = []
    # 加载 casbin
    e = await get_casbin()
    # 查看父级角色的权限，再拿当前提交的权限对比，如果在父角色权限里面则创建，如果不在继续
    p_role_policy = await e.get_permissions_for_user_or_role(p_role_role)  # [[],[],[]]
    all_policy = all_policy + p_role_policy

    # 去重all_policy
    # all_policy = list(set(all_policy))

    # 提交的需要创建的策略
    submit_policy = []
    for obj_act in role_policy.obj_acts:
        submit_policy.append([role_role, obj_act.obj, obj_act.act])

    fail = []
    # 比较all_policy和submit_policy，如果all_policy包含submit_policy则创建，否则报错
    for sp in submit_policy:
        if is_super:
            res = await e.add_policy(sp[0], sp[1], sp[2])
            if not res:
                fail.append(sp)
        else:
            if sp not in all_policy:
                return ResultResponse(code=401,
                                      message='角色权限不足,不能创建高于自身的权限的角色')
            else:
                res = await e.add_policy(sp[0], sp[1], sp[2])
                if not res:
                    fail.append(sp)

    # 如果有失败的策略，返回部分策略已存在
    if fail:
        return ResultResponse[List](message='部分策略已存在', result=fail)
    else:
        return ResultResponse[str](message='添加策略成功')


# 批量创建策略
"""
[
    {
        "user_id": 1,
        "policies": [{
            "type": "api",
            "obj": "/auth",
            "acts": ["GET", "POST", "PUT", "DELETE"]
        },{},{},{}]
    },
    {
        "user_id": 2,
        "policies": [{
            "type": "menu",
            "obj": "/auth",
            "acts": ["show"]
            }]
    }
]
"""


@admin_casbin_router.post(  # todo: 接口可能有问题，没删除
    "-admin-role-list",
    summary="add list user policy",
    description="add list user policy",
    response_model=ResultResponse,
)
async def add_role_policy_list(role_policies: RolePolicyListCreate, current_user: AdminUser = Depends(
    get_current_active_user)) -> ResultResponse:
    print(role_policies.dict(), 7777777777777777777777777777777777777777777777777777777777777777777777777777777777)
    is_super = current_user.is_super
    username = current_user.username
    date = role_policies.dict()
    # 查看角色是否存在，如果不存在报错吗，如果存在继续
    role_orm = await AdminRole.filter(is_deleted=False, id=date["role_id"]).first().values()
    if not role_orm:
        return ResultResponse(code=404, message='用户角色不存在')
    role_role = role_orm.get('role')

    # 查看父级角色是否存在，如果不存在报错，如果存在继续
    p_role_id = role_orm.get('parent_role_id')
    p_role_orm = await AdminRole.filter(is_deleted=False, id=p_role_id).first().values()
    if not p_role_orm:
        return ResultResponse(code=404, message='用户父级角色不存在')
    # print(p_role_orm)
    p_role_role = p_role_orm.get('role')

    all_policy = []
    e = await get_casbin()
    # 获取所有的资源，传入进来的所有的资源和动作，创建
    new_policies = []
    if date["policies"]:
        for item in date["policies"]:
            obj_type = item["type"]
            obj = item["obj"]
            acts_upper = [s.upper() for s in item["acts"]]
            # 创建动作
            acts_orm = await AdminAction.filter(act__in=acts_upper).values()
            acts_act = [d['act'] for d in acts_orm if d['act'] in acts_upper]
            acts_ids = [d['id'] for d in acts_orm if d['act'] in acts_upper]
            for act in acts_upper:
                act_date = {
                    "name": act,
                    "act": act
                }
                if act not in acts_act:
                    act_info = await AdminAction.create(**act_date)
                    acts_act.append(act)
                    acts_ids.append(act_info.id)

            # 创建资源
            obj_date = {
                "name": obj.split('/')[-1],
                "obj": obj,
                "type": obj_type
            }
            # 如果obj是空
            if obj and obj not in [d['obj'] for d in await AdminObject.filter(obj=obj, type=obj_type).values()]:
                admin_object_orm = await AdminObject.create(**obj_date)
                for act_id in acts_ids:
                    admin_action = await AdminAction.get_or_none(id=act_id)
                    if admin_action:
                        await admin_object_orm.acts.add(admin_action)

            # 创建新策略，这里的act全部是大写过后的
            for act in acts_act:
                new_policies.append([role_role, obj, act])

        # 查看父级角色的权限，再拿当前提交的权限对比，如果在父角色权限里面则创建，如果不在继续
        p_role_policy = await e.get_permissions_for_user_or_role(p_role_role)  # [[],[],[]]
        all_policy = all_policy + p_role_policy
        for sp in new_policies:
            if is_super:
                pass
            else:
                if sp not in all_policy:
                    return ResultResponse(code=401,
                                          message='角色权限不足,不能创建高于自身的权限的角色')

        # 先找到该角色下的所有策略，循环删除。然后遍历创建
        had_role_policies = await e.get_permissions_for_user_or_role(role_role)
        for had_role_policy in had_role_policies:
            await e.remove_policy(had_role_policy[0], had_role_policy[1], had_role_policy[2])

        # 创建新策略
        for new_policy in new_policies:
            await e.add_policy(new_policy[0], new_policy[1], new_policy[2])

        return ResultResponse[str](message='策略添加成功')
    else:
        had_role_policies = await e.get_permissions_for_user_or_role(role_role)
        for had_role_policy in had_role_policies:
            await e.remove_policy(had_role_policy[0], had_role_policy[1], had_role_policy[2])
        return ResultResponse[str](message='清空策略成功')


@admin_casbin_router.delete(
    '-admin-role',
    summary='del policy',
    description='del policy',
    response_model=ResultResponse,
)
async def del_role_policy(policy: PolicyDelQuery = Depends()):
    e = await get_casbin()
    res = await e.remove_policy(policy.sub, policy.obj, policy.act)
    if res:
        return ResultResponse[str](message='删除策略成功')
    return ResultResponse[str](code=404, message='策略不存在')


@admin_casbin_router.get(
    '-user-role',
    summary='get user role',
    description='get user role',
    response_model=ResultResponse[ListResult[UserRoleOut]]
)
async def get_user_roles(user_role: UserRoleOutQuery = Depends()):
    admin_user_orm = await AdminUser.get_or_none(id=user_role.user_id)
    if not admin_user_orm:
        return ResultResponse(code=HttpStatus.HTTP_404, message='用户不存在')
    e = await get_casbin()
    results = await e.get_roles_for_user(admin_user_orm.username)
    return ResultResponse[ListResult[UserRoleOut]](
        result=ListResult[UserRoleOut](
            data=[UserRoleOut(username=admin_user_orm.username, role=result) for result in results],
            all_count=len(results)
        ),
    )


@admin_casbin_router.post(
    "-user-role",
    summary="add user role",
    description="add user role",
    response_model=ResultResponse[str],
)
async def add_user_role(user_role: UserRoleCreate):
    admin_user_orm = await AdminUser.get_or_none(id=user_role.user_id)
    # get_or_none()方法，该方法允许我们根据主键（id）查询单个记录，如果找到了符合条件的记录，则返回该记录对应的对象实例
    if not admin_user_orm:
        return ResultResponse(code=HttpStatus.HTTP_404, message='用户不存在')
    role_orm = await AdminRole.get_or_none(id=user_role.role_id)
    # print(role_orm.role)
    if not role_orm:
        return ResultResponse(code=404, message='角色不存在')
    if role_orm.role == 'super_role':
        await AdminUser.filter(id=user_role.user_id).update(is_super=True)
    e = await get_casbin()
    need_delete_roles = await e.get_roles_for_user(admin_user_orm.username)
    for nd in need_delete_roles:
        if nd != 'super_role':
            await e.remove_policy(admin_user_orm.username, nd)

    # 为保证一个用户一个角色，在添加角色的时候，删除以前关联的角色
    is_deleted = await e.delete_roles_for_user(admin_user_orm.username)
    # print(is_deleted)
    res = False
    if is_deleted or not need_delete_roles:
        res = await e.add_role_for_user(admin_user_orm.username, role_orm.role)
        # print(res)

    if res:
        return ResultResponse[str](message='添加用户角色成功')
    else:
        return ResultResponse(code=500, message='添加用户角色失败')


@admin_casbin_router.delete(
    '-user-role-policy',
    summary='del policy',
    description='del policy',
    response_model=ResultResponse,
)
async def del_user_role_policy(user_role: UserRoleDelQuery = Depends()):
    e = await get_casbin()
    res = await e.remove_policy(user_role.username, user_role.role)
    if res:
        return ResultResponse[str](message='删除策略成功')
    return ResultResponse[str](code=500, message='策略删除失败')


@admin_casbin_router.get(
    '-test',
    summary='auth test',
    description='auth test',
    response_model=ResultResponse[bool]
)
async def test_auth(test: PolicyTestQuery = Depends()):
    e = await get_casbin()
    result = await e.has_permission(test.sub, test.obj, test.act)
    return ResultResponse[bool](result=result)
