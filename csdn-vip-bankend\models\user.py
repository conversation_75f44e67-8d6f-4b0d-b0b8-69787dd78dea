from .base import BaseModel, fields
from enum import IntEnum


class ThirdType(IntEnum):
    wechat = 1
    union_pay = 2


class User(BaseModel):
    """用户表"""
    username = fields.Char<PERSON>ield(max_length=128, unique=True, null=False, description="用户名")
    nickname = fields.CharField(max_length=128, null=True, description="昵称")
    hashed_password = fields.CharField(max_length=128, null=True, description="密码")
    avatar = fields.CharField(max_length=256, null=True, description="头像")
    phone = fields.Char<PERSON>ield(max_length=11, unique=True, null=True, description="主要号码")
    is_active = fields.BooleanField(default=False, description="是否激活")

    class Meta:
        table = "user"

    class PydanticMeta:
        exclude = ['is_deleted']


class UserThirdAccount(BaseModel):
    """第三方账号表"""
    user = fields.ForeignKeyField('template.User', related_name='third_account', description="用户")
    third_unique_account = fields.CharField(max_length=128, null=False, description="第三方唯一用户id")
    third_type: ThirdType = fields.IntEnumField(ThirdType, description="标识第三方类型")

    class Meta:
        table = "user_third_account"

    class PydanticMeta:
        exclude = ["is_deleted"]

