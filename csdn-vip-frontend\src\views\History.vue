<template>
  <div class="history-container">
    <div class="header-section">
      <h1>提取历史</h1>
      <div class="header-actions">
        <el-button @click="refreshHistory" :loading="isLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-card>
        <el-form :inline="true" :model="filterForm">
          <el-form-item label="状态筛选">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
              <el-option label="全部" :value="null" />
              <el-option label="成功" :value="3" />
              <el-option label="失败" :value="4" />
              <el-option label="重复" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadHistory">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-list">
      <el-card v-loading="isLoading">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>提取记录</span>
            <div class="header-info">
              <span>共 {{ total }} 条记录</span>
            </div>
          </div>
        </template>

        <div v-if="historyList.length === 0 && !isLoading" class="empty-state">
          <el-empty description="暂无提取记录">
            <el-button type="primary" @click="goToExtract">
              <el-icon><DocumentAdd /></el-icon>
              开始提取
            </el-button>
          </el-empty>
        </div>

        <el-table v-else :data="historyList" style="width: 100%">
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag 
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="title" label="文章标题" show-overflow-tooltip>
            <template #default="scope">
              <div class="title-cell">
                <span>{{ scope.row.title || '未获取到标题' }}</span>
                <el-button 
                  text 
                  type="primary" 
                  size="small"
                  @click="openUrl(scope.row.url)"
                >
                  <el-icon><Link /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="author" label="作者" width="120" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.author || '未知' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="content_length" label="内容长度" width="120">
            <template #default="scope">
              {{ scope.row.content_length ? formatBytes(scope.row.content_length) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="request_time" label="提取时间" width="180">
            <template #default="scope">
              {{ formatTime(scope.row.request_time) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-dropdown trigger="click">
                <el-button text type="primary">
                  <el-icon><More /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="viewDetails(scope.row)">
                      <el-icon><View /></el-icon>
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="scope.row.content" 
                      @click="copyContent(scope.row.content)"
                    >
                      <el-icon><CopyDocument /></el-icon>
                      复制内容
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="scope.row.content" 
                      @click="downloadContent(scope.row)"
                    >
                      <el-icon><Download /></el-icon>
                      下载内容
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="提取详情" 
      width="70%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文章URL">
            <el-link :href="selectedRecord.url" target="_blank" type="primary">
              {{ selectedRecord.url }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="标题">
            {{ selectedRecord.title || '未获取到标题' }}
          </el-descriptions-item>
          <el-descriptions-item label="作者">
            {{ selectedRecord.author || '未知作者' }}
          </el-descriptions-item>
          <el-descriptions-item label="提取时间">
            {{ formatTime(selectedRecord.request_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ selectedRecord.complete_time ? formatTime(selectedRecord.complete_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="内容长度" :span="2">
            {{ selectedRecord.content_length ? formatBytes(selectedRecord.content_length) : '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedRecord.content" class="content-section">
          <h4>文章内容：</h4>
          <div class="content-display">
            {{ selectedRecord.content }}
          </div>
          <div class="content-actions">
            <el-button @click="copyContent(selectedRecord.content)">
              <el-icon><CopyDocument /></el-icon>
              复制内容
            </el-button>
            <el-button @click="downloadContent(selectedRecord)">
              <el-icon><Download /></el-icon>
              下载内容
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Search, Clock, DocumentAdd, Link, More, View, 
  CopyDocument, Download 
} from '@element-plus/icons-vue'
import { useExtractionStore } from '../stores/extraction'
import extractionApi from '../api/extraction'
import type { ExtractionRecord } from '../api/extraction'

const router = useRouter()
const extractionStore = useExtractionStore()

// 状态
const isLoading = ref(false)
const historyList = ref<ExtractionRecord[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 筛选表单
const filterForm = reactive({
  status: null as number | null
})

// 详情对话框
const detailDialogVisible = ref(false)
const selectedRecord = ref<ExtractionRecord | null>(null)

// 状态映射
const statusMap = {
  1: { text: '等待中', type: 'info' },
  2: { text: '处理中', type: 'warning' },
  3: { text: '成功', type: 'success' },
  4: { text: '失败', type: 'danger' },
  5: { text: '重复', type: 'warning' }
}

// 获取状态文本
const getStatusText = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.text || '未知'
}

// 获取状态类型
const getStatusType = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 格式化字节数
const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 字符'
  return `${bytes.toLocaleString()} 字符`
}

// 加载历史记录
const loadHistory = async () => {
  if (!extractionStore.currentToken) {
    ElMessage.error('请先验证访问令牌')
    router.push('/')
    return
  }

  isLoading.value = true

  try {
    const response = await extractionApi.getExtractionHistory({
      token: extractionStore.currentToken,
      page: currentPage.value,
      page_size: pageSize.value,
      status: filterForm.status || undefined
    })

    historyList.value = response.items
    total.value = response.total
    extractionStore.setExtractionHistory(response.items)
  } catch (error) {
    console.error('Load history failed:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    isLoading.value = false
  }
}

// 刷新历史记录
const refreshHistory = () => {
  currentPage.value = 1
  loadHistory()
}

// 分页变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadHistory()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadHistory()
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 查看详情
const viewDetails = (record: ExtractionRecord) => {
  selectedRecord.value = record
  detailDialogVisible.value = true
}

// 复制内容
const copyContent = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('内容已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 下载内容
const downloadContent = (record: ExtractionRecord) => {
  if (!record.content) return

  const title = record.title || 'article'
  const blob = new Blob([record.content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${title}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('文件下载已开始')
}

// 跳转到提取页面
const goToExtract = () => {
  router.push('/extract')
}

// 页面初始化
onMounted(async () => {
  // 检查token有效性
  if (!extractionStore.currentToken) {
    ElMessage.warning('请先验证访问令牌')
    router.push('/')
    return
  }

  // 如果没有token信息，重新验证
  if (!extractionStore.tokenInfo) {
    try {
      const response = await extractionApi.validateToken({
        token: extractionStore.currentToken
      })

      if (!response.valid) {
        ElMessage.error('令牌已失效，请重新验证')
        extractionStore.clearAll()
        router.push('/')
        return
      }

      extractionStore.setTokenInfo(response)
    } catch (error) {
      console.error('Token validation failed:', error)
      ElMessage.error('令牌验证失败')
      router.push('/')
      return
    }
  }

  // 加载历史记录
  await loadHistory()
})
</script>

<style scoped>
.history-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-section h1 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.filter-section {
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header > div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.header-info {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.title-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.content-section {
  margin-top: 2rem;
}

.content-section h4 {
  margin-bottom: 1rem;
  color: #303133;
}

.content-display {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.content-actions {
  display: flex;
  gap: 1rem;
}

@media (max-width: 768px) {
  .history-container {
    padding: 1rem;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .title-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .content-actions {
    flex-direction: column;
  }
}
</style>
