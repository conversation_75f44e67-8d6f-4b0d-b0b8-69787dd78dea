from typing import Any, Callable, List, Type, cast, Coroutine, Optional, Union
from . import CRUDGenerator, NOT_FOUND
from ._types import DE<PERSON>ENDENCIES, PAGINATION, PYDANTIC_SCHEMA as SCHEMA
from core.response import ResultResponse, ListResult

try:
    from tortoise.models import Model
except ImportError:
    Model = None  # type: ignore
    tortoise_installed = False
else:
    tortoise_installed = True

CALLABLE = Callable[..., Coroutine[Any, Any, ResultResponse[SCHEMA]]]
CALLABLE_LIST = Callable[..., Coroutine[Any, Any, ResultResponse[ListResult[SCHEMA]]]]


class TortoiseCRUDRouter(CRUDGenerator[SCHEMA]):
    def __init__(
            self,
            db_model: Type[Model],
            list_schema: Type[SCHEMA],
            detail_schema: Type[SCHEMA] = None,
            create_schema: Optional[Type[SCHEMA]] = None,
            update_schema: Optional[Type[SCHEMA]] = None,
            prefix: Optional[str] = None,
            tags: Optional[List[str]] = None,
            paginate: Optional[int] = None,
            get_all_route: Union[bool, DEPENDENCIES] = True,
            get_one_route: Union[bool, DEPENDENCIES] = True,
            create_route: Union[bool, DEPENDENCIES] = True,
            update_route: Union[bool, DEPENDENCIES] = True,
            delete_one_route: Union[bool, DEPENDENCIES] = True,
            delete_all_route: Union[bool, DEPENDENCIES] = False,
            **kwargs: Any
    ) -> None:
        assert (
            tortoise_installed
        ), "Tortoise ORM must be installed to use the TortoiseCRUDRouter."

        self.db_model = db_model
        self._pk: str = db_model.describe()["pk_field"]["db_column"]

        super().__init__(
            list_schema=list_schema,
            detail_schema=detail_schema,
            create_schema=create_schema,
            update_schema=update_schema,
            prefix=prefix or db_model.describe()["name"].replace("None.", ""),
            tags=tags,
            paginate=paginate,
            get_all_route=get_all_route,
            get_one_route=get_one_route,
            create_route=create_route,
            update_route=update_route,
            delete_one_route=delete_one_route,
            delete_all_route=delete_all_route,
            **kwargs
        )

    def _get_all(self, *args: Any, **kwargs: Any) -> CALLABLE_LIST:
        async def route(pagination: PAGINATION = self.pagination) -> ResultResponse[ListResult[SCHEMA]]:
            skip, limit = pagination.get("skip"), pagination.get("limit")
            query = self.db_model.filter(is_deleted=False)
            all_count = await query.count()
            query = query.all().order_by('id').offset(cast(int, skip))
            if limit:
                query = query.limit(limit)
            data = await self.list_schema.from_queryset(query)
            return ResultResponse[ListResult[self.list_schema]](
                result=ListResult[self.list_schema](
                    all_count=all_count,
                    data=data
                )
            )

        return route

    def _get_one(self, *args: Any, **kwargs: Any) -> CALLABLE:
        async def route(item_id: int) -> ResultResponse[SCHEMA]:
            model = await self.db_model.get_or_none(id=item_id, is_deleted=False)

            if model:
                return ResultResponse[self.detail_schema](result=await self.detail_schema.from_tortoise_orm(model))
            else:
                raise NOT_FOUND

        return route

    def _create(self, *args: Any, **kwargs: Any) -> CALLABLE:
        async def route(model: self.create_schema) -> ResultResponse[SCHEMA]:  # type: ignore
            data = await self.detail_schema.from_tortoise_orm(await self.db_model.create(**model.dict()))
            return ResultResponse[self.detail_schema](result=data)

        return route

    def _update(self, *args: Any, **kwargs: Any) -> CALLABLE:
        async def route(
                item_id: int, model: self.update_schema  # type: ignore
        ) -> ResultResponse[SCHEMA]:
            result = await self.db_model.filter(id=item_id, is_deleted=False).update(
                **model.dict(exclude_unset=True)
            )
            if result == 0:
                raise NOT_FOUND
            else:
                return await self._get_one()(item_id)

        return route

    def _delete_all(self, *args: Any, **kwargs: Any) -> CALLABLE_LIST:
        async def route() -> ResultResponse[ListResult[SCHEMA]]:
            await self.db_model.all().delete()
            return await self._get_all()(pagination={"skip": 0, "limit": None})

        return route

    def _delete_one(self, *args: Any, **kwargs: Any) -> CALLABLE:
        async def route(item_id: int) -> ResultResponse[SCHEMA]:
            result = await self._get_one()(item_id)
            await self.db_model.filter(id=item_id).delete()

            return result

        return route
