import api from './index'

// 类型定义
export interface ExtractionRequest {
  url: string
  token: string
  force_refresh?: boolean
}

export interface ExtractionResponse {
  success: boolean
  message: string
  data?: any
  is_duplicate?: boolean
  error_code?: string
}

export interface ExtractionRecord {
  id: number
  url: string
  title?: string
  author?: string
  status: number
  request_time: string
  complete_time?: string
  content?: string
  content_length?: number
}

export interface ExtractionHistoryRequest {
  token: string
  page?: number
  page_size?: number
  status?: number
}

export interface ExtractionHistoryResponse {
  total: number
  page: number
  page_size: number
  pages: number
  items: ExtractionRecord[]
}

export interface TokenValidationRequest {
  token: string
  url?: string
  client_ip?: string
}

export interface TokenValidationResponse {
  valid: boolean
  token_info?: any
  remaining_count?: number
  remaining_time?: string
  error_message?: string
}

// API接口
export const extractionApi = {
  // 验证token
  validateToken: (data: TokenValidationRequest) => {
    return api.post<TokenValidationResponse>('/access-token/validate', data)
  },

  // 提取单篇文章
  extractArticle: (data: ExtractionRequest) => {
    return api.post<ExtractionResponse>('/extraction/extract', data)
  },

  // 批量提取文章
  batchExtractArticles: (data: { urls: string[], token: string, force_refresh?: boolean }) => {
    return api.post('/extraction/batch-extract', data)
  },

  // 获取提取历史
  getExtractionHistory: (data: ExtractionHistoryRequest) => {
    return api.post<ExtractionHistoryResponse>('/extraction/history', data)
  }
}

export default extractionApi
