import uuid

from tortoise import fields, models


class BaseModel(models.Model):
    id = fields.IntField(pk=True, description="id")
    uuid = fields.UUIDField(null=False, default=uuid.uuid4, unique=True, description="uuid")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")
    create_by = fields.CharField(max_length=20, default="admin", null=True, description="创建人")
    update_by = fields.CharField(max_length=20, default="admin", null=True, description="更新人")
    remark = fields.TextField(default=None, null=True, description="备注")
    is_deleted = fields.BooleanField(default=False, description="逻辑删除标记")

    class Meta:
        abstract = True
