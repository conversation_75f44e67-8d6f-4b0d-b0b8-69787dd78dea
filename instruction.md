项目描述：


这是一个csdn账号权限中转系统，使用token进行身份识别以及套餐管理。用户通过携带着token的链接访问文章提交页面，提交需要提取的csdn文章。

token权限类型有时间限制(但是防止刷量依旧限制次数)，次数类型不限制时间。
用户带着token访问页面提交url之后，后端系统请求接口接收请求接口，系统需要进行权限验证并把链接分发给csdn文章获取核心工具(是我的csdn提取的核心，会暴露接口出来，当前中转系统只需要调用接口就行了。当然提取接口也是需要系统的验证密钥，可以写死在系统中，调用的时候需要也叫token字段。目前量少就部署一个获取核心就行了)的接口。

本系统要做的就是

token管理，可快速生成访问链接以便发给用户，
提取核心客户端api端点和授权密钥管理，
提取核心对接中转，
需要有提取的历史记录管理，本系统记录所有已经提取过的文章提取过就不重复提取，返回给用户时根据用户token区分，
还需要有后台用户管理，
后台管理用户的个人中心支持修改个人信息和密码，
系统要做防止恶意刷接口的功能和设计
