import re
import os
import json
import time
from urllib import parse
from utils.base import generate_random_str
import aiohttp
import platform
import asyncio
import traceback
from typing import Union


class ZGIOData:
    def __init__(self):
        self.headers = {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "cache-control": "no-cache",
                    "content-type": "application/x-www-form-urlencoded",
                    "pragma": "no-cache",
                    "sec-ch-ua": "\"Chromium\";v=\"110\", \"Not A(Brand\";v=\"24\", \"Microsoft Edge\";v=\"110\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "Cookie": "Hm_lvt_5e3cb1a4d6d94b24154c753e64074c73=1678688681; _ga=GA1.2.1434864086.1678688681; wooTracker=moOfQYWFvEBv; demo_info=true; amplitude_idzhugeio.com=eyJkZXZpY2VJZCI6IjJmNTI3NzE3LTUxYzItNDBiYi05ODVmLWY3NTliYTIzNmU0OVIiLCJ1c2VySWQiOm51bGwsIm9wdE91dCI6ZmFsc2UsInNlc3Npb25JZCI6MTY3ODY5NzYyMzEwNSwibGFzdEV2ZW50VGltZSI6MTY3ODY5NzczNDI0OCwiZXZlbnRJZCI6NTYsImlkZW50aWZ5SWQiOjAsInNlcXVlbmNlTnVtYmVyIjo1Nn0=; SECKEY_ABVK=ubZ+kqVHKfc7Ovk2NvjdCzsEhuwGKX5tIdep0FkFcVM%3D; BMAP_SECKEY=bVVLHGkPS4t15XVekhx7r_aA1b2QNEzBUHftEbJaKumQf662bdDjgR6KTNty0iW72z98Oao7lqA8Jzm41A9hFWBU9gDuKrJl90gevSqrB15IRaP2Q5l4TITA9Knga2SD9bxYyoPNQSxbYZQijsQC_GSrU8y9kWAL5a4pTDx9_5QjDkUKSmzaE2M2jjYWan_u; connect.sid=s%3AV9zZ8IvEc31_mf5w-BOHdTbI8uobO7oy.jOKYkoNW%2BFoNXCPKZo1%2FMeyOyk6KR%2B2AHDEk1biax%2F0; JSESSIONID=94642B268D5CCDF74290A87D8E704E95; z-session-ticket=03eb525677b3446ad5acc8a192f21d75; z-qbi-login=4e4782da3cec4f36ae3e30e631c7683e; zg_did=%7B%22did%22%3A%20%22186d9a47b926a7-01a11b08a95ae-7452547c-384000-186d9a47b9312ab%22%7D; zg_5f8a935cd15a47419df0e07d0547551b=%7B%22sid%22%3A%201678858155773%2C%22updated%22%3A%201678858164312%2C%22info%22%3A%201678688680855%2C%22superProperty%22%3A%20%22%7B%7D%22%2C%22platform%22%3A%20%22%7B%7D%22%2C%22utm%22%3A%20%22%7B%7D%22%2C%22referrerDomain%22%3A%20%22demo.zhugeio.com%22%2C%22landHref%22%3A%20%22https%3A%2F%2Fzhugeio.com%2Fpage%2Fproduct%2Fmatrix%2Fcdp%22%2C%22prePath%22%3A%20%22https%3A%2F%2Fzhugeio.com%2F%22%2C%22duration%22%3A%201569.1380000002682%2C%22cuid%22%3A%20%2218175315870%22%2C%22zs%22%3A%200%2C%22sc%22%3A%200%2C%22firstScreen%22%3A%201678858155773%7D"
        }

    @staticmethod
    def response_wrapper(response: str) -> dict:
        response = response.replace('诸葛', 'Cloud')\
            .replace('zhuge', 'cloud')\
            .replace('恒泰', '客户')\
            .replace('hengtai', 'kehu')
        return json.loads(response)

    async def get_data_list(self, data: dict):
        print('正在分析数据...')
        try:
            api_url = 'https://demo.zhugeio.com/analysis/api/common/proxy/data/datalist.jsp'
            self.headers['referer'] = 'https://demo.zhugeio.com/webapp/app/3/plat/0/analysis/wholeAnalysis?range=1,1,relative&graph=line&seg=hour&app_id=3&p=0&module=add&m=number'
            print(f"正在分析数据API: {api_url}")
            async with aiohttp.ClientSession() as session:
                async with session.post(api_url, headers=self.headers, data=data, timeout=10) as response:
                    print(response.request_info)
                    response = await response.text()
                    print(f'分析数据成功！{response}')
                    return self.response_wrapper(response)['app_data']
        except Exception as e:
            print(f'分析数据失败！原因:{e}')
            return None


def get_zzio_data():
    return ZGIOData()