[development]
APP.DEBUG = false
APP.TITLE = "CSDN文章提取系统"
APP.DESCRIPTION = "生产环境"
APP.DOCS_URL = "/openapi/docs"
APP.OPENAPI_URL = "/openapi/openapi.json"
APP.REDOC_URL = "/openapi/redoc"
APP.API_VERSION = "/api/v1"
APP.LOG_PATH = "./log"

# SQLite数据库配置
SQLITE.DATABASE = "data/csdn_vip.db"

# JWT鉴权
JWT.ALGORITHM = "HS256"
JWT.SECRET_KEY = "your_very_secure_secret_key_here_change_in_production"
JWT.ACCESS_TOKEN_EXPIRE_MINUTES = 10080

# 腾讯COS
TENCENT.COS.SECRET_ID = "your_secret_id"
TENCENT.COS.SECRET_KEY = "your_secret_key"
TENCENT.COS.BUCKET = "your_bucket"
TENCENT.COS.REGION = "ap-shanghai"

# CASBIN
CASBIN.CASBIN_MODEL_PATH = "config/rbac.conf"

# 时区
TIMEZONE = "Asia/Shanghai"