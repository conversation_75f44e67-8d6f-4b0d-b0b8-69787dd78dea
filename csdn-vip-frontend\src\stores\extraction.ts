import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ExtractionRecord, TokenValidationResponse } from '../api/extraction'

export const useExtractionStore = defineStore('extraction', () => {
  // 状态
  const currentToken = ref<string>('')
  const tokenInfo = ref<TokenValidationResponse | null>(null)
  const extractionHistory = ref<ExtractionRecord[]>([])
  const isLoading = ref(false)
  const currentRecord = ref<ExtractionRecord | null>(null)

  // 计算属性
  const isTokenValid = computed(() => {
    return tokenInfo.value?.valid === true
  })

  const remainingCount = computed(() => {
    return tokenInfo.value?.remaining_count
  })

  const remainingTime = computed(() => {
    return tokenInfo.value?.remaining_time
  })

  // 方法
  const setToken = (token: string) => {
    currentToken.value = token
    // 保存到localStorage
    if (token) {
      localStorage.setItem('extraction_token', token)
    } else {
      localStorage.removeItem('extraction_token')
    }
  }

  const setTokenInfo = (info: TokenValidationResponse) => {
    tokenInfo.value = info
  }

  const addExtractionRecord = (record: ExtractionRecord) => {
    extractionHistory.value.unshift(record)
  }

  const setExtractionHistory = (history: ExtractionRecord[]) => {
    extractionHistory.value = history
  }

  const setCurrentRecord = (record: ExtractionRecord | null) => {
    currentRecord.value = record
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const clearAll = () => {
    currentToken.value = ''
    tokenInfo.value = null
    extractionHistory.value = []
    currentRecord.value = null
    localStorage.removeItem('extraction_token')
  }

  // 初始化时从localStorage恢复token
  const initToken = () => {
    const savedToken = localStorage.getItem('extraction_token')
    if (savedToken) {
      currentToken.value = savedToken
    }
  }

  return {
    // 状态
    currentToken,
    tokenInfo,
    extractionHistory,
    isLoading,
    currentRecord,
    
    // 计算属性
    isTokenValid,
    remainingCount,
    remainingTime,
    
    // 方法
    setToken,
    setTokenInfo,
    addExtractionRecord,
    setExtractionHistory,
    setCurrentRecord,
    setLoading,
    clearAll,
    initToken
  }
})
