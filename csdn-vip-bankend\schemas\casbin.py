from typing import Optional, List
from fastapi import Query
from models import AdminRole, AdminObject, AdminAction
from schemas import AdminUserOut
from schemas.base import BaseListQueryModel, BaseQueryModel
from utils.model_pydantic import pydantic_model_creator
from pydantic import BaseModel, Field

AdminRoleOut = pydantic_model_creator(
    AdminRole,
    name="AdminRoleOut",
    exclude_relations=False,
    exclude_backward_relations=False,
    allow_cycles=True,
)

class UsersInAdminRoleOut(AdminRoleOut):
    users: Optional[List[dict]] = Field(..., description="角色下的所有用户")


AdminRoleCreateBase = pydantic_model_creator(    # 创建角色基础，即创建的角色不需要有哪些字段role_info: AdminRoleCreateBase
    AdminRole,                                   # role_info指的是
    name="AdminRoleCreateBase",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'is_deleted', 'remark', 'role_admin_id'
    ),
    exclude_relations=True,
)


AdminRoleUpdate = pydantic_model_creator(
    AdminRole,
    name="AdminRoleUpdate",
    exclude=(
        'id', 'create_time', 'update_time', 'create_by', 'update_by', 'is_deleted', 'remark', 'parent_role_id'
    ),
    exclude_relations=True,
)


class PolicyOut(BaseModel):
    sub: Optional[str] = Field(..., description='主题(访问实体)')
    obj: Optional[str] = Field(..., description='目标(访问资源)')
    act: Optional[str] = Field(..., description='动作(访问方式)')

    class Config:
        """schema_extra中设置参数的例子，在API文档中可以看到"""
        schema_extra = {
            'example': {
                'sub': '管理员',
                'obj': '/auth',
                'act': 'get'
            }
        }


class PolicyWithTypeOut(PolicyOut):
    type: Optional[str] = Field(..., description='资源类型(访问方式)')


class UserRoleOut(BaseModel):
    username: str = Field(..., description='用户名')
    role: str = Field(..., description='角色')

    class Config:
        """schema_extra中设置参数的例子，在API文档中可以看到"""
        schema_extra = {
            'example': {
                'username': 'zhang',
                'role': 'role_administer',
            }
        }


class UserRoleCreate(BaseModel):
    user_id: str = Field(..., description='用户ID')
    role_id: str = Field(..., description='角色ID')

    class Config:
        """schema_extra中设置参数的例子，在API文档中可以看到"""
        schema_extra = {
            'example': {
                'user_id': 11,
                'role_id': 22,
            }
        }


class ObjAct(BaseModel):
    obj: str = Field(..., description='目标(访问资源)')
    act: str = Field(..., description='动作(访问方式)')


# 角色权限创建
class RolePolicyCreate(BaseModel):
    role_id: str = Field(..., description='角色ID')
    obj_acts: List[ObjAct] = Field(..., description='权限组')


# 批量创建策略
"""
[
    {
        "user_id": 1,
        "policies": [{
            "objs": "/auth",
            "acts": ["get","post","put","delete"]
        },{},{},{}]
    },
    {
        "user_id": 2,
        "policies": [{
            "objs": "/auth",
            "acts": ["get","post","put","delete"]
            }]
    }
]
"""


class RolePolicyListCreate(BaseModel):
    role_id: int = Field(..., description='角色ID')
    policies: List[dict] = Field(..., description='权限们')

    class Config:
        """schema_extra中设置参数的例子，在API文档中可以看到"""
        schema_extra = {
            "role_id": 1,
            "policies": [
                {
                    "type": "api",
                    "obj": "/admin_user",
                    "acts": ["GET", "POST", "PUT", "DELETE"]
                },
                {
                    "type": "menu",
                    "obj": "/metadata",
                    "acts": ["show"]
                },
                {
                    "type": "",
                    "obj": "",
                    "acts": []
                }
            ]
        }


class AdminUserPolicyCreate(BaseModel):
    admin_user_id: str = Field(..., description='ADMIN用户ID')
    obj_acts: List[ObjAct] = Field(..., description='权限组')


class RolePolicyOutQuery(BaseQueryModel):
    def __init__(
            self,
            role_id: Optional[str] = Query(default=None, description='角色ID'),
    ):
        self.role_id = role_id


class UserPolicyOutQuery(BaseQueryModel):
    def __init__(
            self,
            user_id: Optional[str] = Query(default=None, description='用户ID'),
    ):
        self.user_id = user_id


UserRoleOutQuery = UserPolicyOutQuery


class PolicyDelQuery(BaseQueryModel):
    def __init__(
            self,
            sub: str = Query(default=None, description='主题(访问实体)'),
            obj: str = Query(default=None, description='目标(访问资源)'),
            act: str = Query(default=None, description='动作(访问方式)'),
    ):
        self.sub = sub
        self.obj = obj
        self.act = act


PolicyTestQuery = PolicyDelQuery


class UserRoleDelQuery(BaseQueryModel):
    def __init__(
            self,
            username: str = Query(default=None, description='用户名'),
            role: str = Query(default=None, description='角色'),

    ):
        self.username = username
        self.role = role


AdminObjectOut = pydantic_model_creator(
    AdminObject,
    name="AdminObjectOut",
)

AdminObjectCreateAndUpdateBase = pydantic_model_creator(
    AdminObject,
    name="AdminObjectCreateAndUpdateBase",
    exclude=(
        'id', 'create_time', 'update_time', "create_by", "remark"
    ),
    exclude_relations=True
)


class AdminObjectCreateAndUpdate(AdminObjectCreateAndUpdateBase):
    acts: Optional[List[int]] = Field(default=[], description='动作(访问方式)列表')


AdminActionOut = pydantic_model_creator(
    AdminAction,
    name="AdminActionOut",
    exclude_relations=True
)

AdminActionCreateAndUpdate = pydantic_model_creator(
    AdminAction,
    name="AdminActionCreateAndUpdate",
    exclude=(
        'id', 'create_time', 'update_time', "create_by", "remark"
    ),
    exclude_relations=True
)


class AdminCasbinObjectQueryByName(BaseListQueryModel):
    def __init__(
            self,
            limit: Optional[int] = Query(default=None),
            skip: int = Query(default=0),
            name: Optional[str] = Query(default=None, description='名称'),
            obj: Optional[str] = Query(default=None, description='目标(访问资源)'),
            type: Optional[str] = Query(default=None, description='资源类型'),
            role: Optional[str] = Query(default=None, description='角色')
    ):
        self.name = name
        self.obj = obj
        self.type = type
        self.role = role
        super().__init__(
            limit=limit,
            skip=skip,
        )
