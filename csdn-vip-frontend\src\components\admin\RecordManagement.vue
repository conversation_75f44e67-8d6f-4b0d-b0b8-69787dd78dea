<template>
  <div class="record-management">
    <div class="management-header">
      <el-button @click="refreshRecords">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button @click="exportRecords" :loading="isExporting">
        <el-icon><Download /></el-icon>
        导出记录
      </el-button>
      <el-button type="danger" @click="cleanupRecords">
        <el-icon><DeleteFilled /></el-icon>
        清理过期记录
      </el-button>
      
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文章标题或URL"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="状态筛选">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
            <el-option label="全部" :value="null" />
            <el-option label="等待中" :value="1" />
            <el-option label="处理中" :value="2" />
            <el-option label="成功" :value="3" />
            <el-option label="失败" :value="4" />
            <el-option label="重复" :value="5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="Token">
          <el-input 
            v-model="filterForm.token" 
            placeholder="Token前缀搜索" 
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="applyFilter">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="总记录数" :value="statistics.totalRecords" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="今日提取" :value="statistics.todayRecords" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="成功率" :value="statistics.successRate" suffix="%" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="去重率" :value="statistics.duplicateRate" suffix="%" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 记录列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>提取记录</span>
          <div class="header-info">
            <span>共 {{ filteredRecords.length }} 条记录</span>
          </div>
        </div>
      </template>

      <el-table :data="paginatedRecords" style="width: 100%" v-loading="isLoading">
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="文章标题" show-overflow-tooltip>
          <template #default="scope">
            <div class="title-cell">
              <span>{{ scope.row.title || '未获取到标题' }}</span>
              <el-button 
                text 
                type="primary" 
                size="small"
                @click="openUrl(scope.row.url)"
              >
                <el-icon><ExternalLink /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="author" label="作者" width="120" show-overflow-tooltip />
        
        <el-table-column prop="token" label="Token" width="120">
          <template #default="scope">
            <span class="token-value">{{ maskToken(scope.row.token) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="content_length" label="内容长度" width="120">
          <template #default="scope">
            {{ scope.row.content_length ? formatBytes(scope.row.content_length) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="request_time" label="请求时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.request_time) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="complete_time" label="完成时间" width="180">
          <template #default="scope">
            <span v-if="scope.row.complete_time">
              {{ formatTime(scope.row.complete_time) }}
            </span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-dropdown trigger="click">
              <el-button text type="primary">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="viewDetails(scope.row)">
                    <el-icon><View /></el-icon>
                    查看详情
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="scope.row.content" 
                    @click="copyContent(scope.row.content)"
                  >
                    <el-icon><CopyDocument /></el-icon>
                    复制内容
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="scope.row.content" 
                    @click="downloadContent(scope.row)"
                  >
                    <el-icon><Download /></el-icon>
                    下载内容
                  </el-dropdown-item>
                  <el-dropdown-item 
                    @click="retryExtraction(scope.row)"
                    v-if="scope.row.status === 4"
                  >
                    <el-icon><RefreshRight /></el-icon>
                    重试提取
                  </el-dropdown-item>
                  <el-dropdown-item 
                    @click="deleteRecord(scope.row)"
                    divided
                  >
                    <el-icon><Delete /></el-icon>
                    删除记录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions">
        <el-button @click="batchDelete" :disabled="!selectedRecords.length">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button @click="batchExport" :disabled="!selectedRecords.length">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredRecords.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="showDetailDialog" 
      title="提取详情" 
      width="70%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            {{ selectedRecord.id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文章URL" :span="2">
            <el-link :href="selectedRecord.url" target="_blank" type="primary">
              {{ selectedRecord.url }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="标题" :span="2">
            {{ selectedRecord.title || '未获取到标题' }}
          </el-descriptions-item>
          <el-descriptions-item label="作者">
            {{ selectedRecord.author || '未知作者' }}
          </el-descriptions-item>
          <el-descriptions-item label="Token">
            <span class="token-value">{{ selectedRecord.token }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="请求时间">
            {{ formatTime(selectedRecord.request_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ selectedRecord.complete_time ? formatTime(selectedRecord.complete_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="内容长度" :span="2">
            {{ selectedRecord.content_length ? formatBytes(selectedRecord.content_length) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" :span="2" v-if="selectedRecord.error_message">
            <el-text type="danger">{{ selectedRecord.error_message }}</el-text>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedRecord.content" class="content-section">
          <h4>文章内容：</h4>
          <div class="content-display">
            {{ selectedRecord.content }}
          </div>
          <div class="content-actions">
            <el-button @click="copyContent(selectedRecord.content)">
              <el-icon><CopyDocument /></el-icon>
              复制内容
            </el-button>
            <el-button @click="downloadContent(selectedRecord)">
              <el-icon><Download /></el-icon>
              下载内容
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Download, DeleteFilled, Search, Document, ExternalLink,
  More, View, CopyDocument, Delete, RefreshRight
} from '@element-plus/icons-vue'

// 模拟数据
const recordList = ref([
  {
    id: 1,
    url: 'https://blog.csdn.net/user1/article/details/123456',
    title: 'Spring Boot 微服务架构实战',
    author: 'tech_guru',
    token: 'tk_abc123def456ghi789',
    status: 3,
    content_length: 15420,
    request_time: '2024-01-15T10:30:00',
    complete_time: '2024-01-15T10:30:15',
    content: '这是一篇关于Spring Boot微服务架构的详细教程...',
    error_message: null
  },
  {
    id: 2,
    url: 'https://blog.csdn.net/user2/article/details/789012',
    title: 'Vue3 响应式原理深度解析',
    author: 'frontend_master',
    token: 'tk_xyz789uvw456rst',
    status: 3,
    content_length: 12890,
    request_time: '2024-01-15T10:25:00',
    complete_time: '2024-01-15T10:25:08',
    content: '本文将深入探讨Vue3的响应式系统实现原理...',
    error_message: null
  },
  {
    id: 3,
    url: 'https://blog.csdn.net/user3/article/details/345678',
    title: 'Python数据分析完整指南',
    author: 'data_scientist',
    token: 'tk_qwe456tyu789iop',
    status: 4,
    content_length: null,
    request_time: '2024-01-15T10:20:00',
    complete_time: '2024-01-15T10:20:30',
    content: null,
    error_message: '文章内容获取失败：访问被拒绝'
  }
])

const isLoading = ref(false)
const isExporting = ref(false)
const showDetailDialog = ref(false)
const selectedRecord = ref<any>(null)
const selectedRecords = ref<any[]>([])
const searchKeyword = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 筛选表单
const filterForm = reactive({
  status: null as number | null,
  dateRange: null as string[] | null,
  token: ''
})

// 统计数据
const statistics = reactive({
  totalRecords: 2156,
  todayRecords: 89,
  successRate: 87.5,
  duplicateRate: 12.3
})

// 状态映射
const statusMap = {
  1: { text: '等待中', type: 'info' },
  2: { text: '处理中', type: 'warning' },
  3: { text: '成功', type: 'success' },
  4: { text: '失败', type: 'danger' },
  5: { text: '重复', type: 'warning' }
}

// 过滤记录列表
const filteredRecords = computed(() => {
  let result = [...recordList.value]
  
  // 关键字搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(record => 
      (record.title?.toLowerCase().includes(keyword)) ||
      record.url.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (filterForm.status !== null) {
    result = result.filter(record => record.status === filterForm.status)
  }
  
  // 时间范围筛选
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [startTime, endTime] = filterForm.dateRange
    result = result.filter(record => {
      const recordTime = new Date(record.request_time).getTime()
      return recordTime >= new Date(startTime).getTime() && 
             recordTime <= new Date(endTime).getTime()
    })
  }
  
  // Token筛选
  if (filterForm.token) {
    result = result.filter(record => 
      record.token.toLowerCase().includes(filterForm.token.toLowerCase())
    )
  }
  
  return result
})

// 分页记录列表
const paginatedRecords = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredRecords.value.slice(start, end)
})

// 获取状态文本
const getStatusText = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.text || '未知'
}

// 获取状态类型
const getStatusType = (status: number) => {
  return statusMap[status as keyof typeof statusMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 格式化字节数
const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 字符'
  return `${bytes.toLocaleString()} 字符`
}

// 隐藏token
const maskToken = (token: string) => {
  if (token.length <= 8) return token
  return token.substring(0, 8) + '...'
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 应用筛选
const applyFilter = () => {
  currentPage.value = 1
}

// 重置筛选
const resetFilter = () => {
  filterForm.status = null
  filterForm.dateRange = null
  filterForm.token = ''
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 刷新记录
const refreshRecords = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('Refresh records failed:', error)
    ElMessage.error('刷新失败')
  } finally {
    isLoading.value = false
  }
}

// 导出记录
const exportRecords = async () => {
  isExporting.value = true
  try {
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export records failed:', error)
    ElMessage.error('导出失败')
  } finally {
    isExporting.value = false
  }
}

// 清理过期记录
const cleanupRecords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理7天前的过期记录吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '清理',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('清理完成，共清理了 156 条过期记录')
  } catch {
    // 用户取消
  }
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 查看详情
const viewDetails = (record: any) => {
  selectedRecord.value = record
  showDetailDialog.value = true
}

// 复制内容
const copyContent = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('内容已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 下载内容
const downloadContent = (record: any) => {
  if (!record.content) return

  const title = record.title || 'article'
  const blob = new Blob([record.content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${title}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('文件下载已开始')
}

// 重试提取
const retryExtraction = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要重新提取这篇文章吗？',
      '提示',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('重试请求已发送')
  } catch {
    // 用户取消
  }
}

// 删除记录
const deleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条记录吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    const index = recordList.value.findIndex(r => r.id === record.id)
    if (index > -1) {
      recordList.value.splice(index, 1)
      ElMessage.success('记录已删除')
    }
  } catch {
    // 用户取消
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedRecords.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRecords.value.length} 条记录吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success(`已删除 ${selectedRecords.value.length} 条记录`)
    selectedRecords.value = []
  } catch {
    // 用户取消
  }
}

// 批量导出
const batchExport = async () => {
  if (selectedRecords.value.length === 0) return
  
  try {
    ElMessage.success(`已导出 ${selectedRecords.value.length} 条记录`)
  } catch (error) {
    console.error('Batch export failed:', error)
    ElMessage.error('批量导出失败')
  }
}
</script>

<style scoped>
.record-management {
  height: 100%;
}

.management-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.search-box {
  margin-left: auto;
  width: 300px;
}

.filter-card {
  margin-bottom: 1.5rem;
}

.stats-row {
  margin-bottom: 1.5rem;
}

.stat-card {
  text-align: center;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.header-info {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.title-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.token-value {
  font-family: 'Courier New', monospace;
  color: #606266;
}

.text-muted {
  color: #c0c4cc;
}

.batch-actions {
  margin: 1rem 0;
  padding: 1rem;
  background-color: #fafafa;
  border-radius: 8px;
  display: flex;
  gap: 1rem;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.content-section {
  margin-top: 2rem;
}

.content-section h4 {
  margin-bottom: 1rem;
  color: #303133;
}

.content-display {
  background-color: #f5f7fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.content-actions {
  display: flex;
  gap: 1rem;
}

@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    margin-left: 0;
    width: 100%;
  }
  
  .stats-row {
    margin-bottom: 1rem;
  }
  
  .batch-actions {
    flex-direction: column;
  }
  
  .content-actions {
    flex-direction: column;
  }
}
</style>
