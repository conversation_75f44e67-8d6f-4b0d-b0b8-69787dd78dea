<table>
  <thead>
    <tr>
      <th>Name</th>
      <th>Methods</th>
      <th>Path</th>
      <th>Endpoint</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    {% for route in routes|sort(attribute='path') %}
      <tr{% if route.endpoint == endpoint %} data-fastdt-styles="backgroundColor: {{ toolbar.settings.LOGGING_COLORS.INFO }}; font-weight: bold;"{% endif %}>
        <td>{{ route.name|default('', true) }}</td>
        <td>{% if route.methods %}{{ route.methods|sort|join(', ') }}{% endif %}</td>
        <td>{{ route.path }}</td>
        <td>{{ get_name_from_obj(route.endpoint) }}</td>
        <td>{{ route.description }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>
