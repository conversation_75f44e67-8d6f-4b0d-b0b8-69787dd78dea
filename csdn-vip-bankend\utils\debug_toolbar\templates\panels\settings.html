{% for settings in toolbar.settings.SETTINGS %}
{% if settings.__config__.title %}<h4>{{ settings.__config__.title }}</h4>{% endif %}

<table>
  <thead>
    <tr>
      <th>Key</th>
      <th>Value</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in settings %}
      <tr>
        <td>{{ key|escape }}</td>
        <td><code>{{ value|pprint|escape }}</code></td>
      </tr>
    {% endfor %}
  </tbody>
</table>
{% endfor %}

<h4>{{ toolbar.settings.__config__.title }}</h4>

<table>
  <thead>
    <tr>
      <th>Key</th>
      <th>Value</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in toolbar.settings %}
      {% if key != 'SETTINGS' %}
      <tr>
        <td>{{ key|escape }}</td>
        <td><code>{{ value|pprint|escape }}</code></td>
      </tr>
      {% endif %}
    {% endfor %}
  </tbody>
</table>
