# 部署指南

本文档详细说明如何部署CSDN文章提取系统。

## 📋 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间

### 软件要求
- 操作系统: Ubuntu 20.04+ / CentOS 7+ / Windows 10+
- Python: 3.8+
- Node.js: 16+
- SQLite3: Python内置，无需额外安装
- Redis: 6+ (可选，用于缓存)

## 🚀 快速部署

### 1. 系统准备

#### Ubuntu/Debian 系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y

# SQLite3已内置在Python中，无需额外安装
```

#### CentOS/RHEL 系统
```bash
# 更新系统包
sudo yum update -y

# 安装Python和pip
sudo yum install python3 python3-pip -y

# 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install nodejs -y

# SQLite3已内置在Python中，无需额外安装
```

### 2. 数据库配置

SQLite3无需额外配置，数据库文件将在首次运行时自动创建。

```bash
# 确保数据目录存在（可选，程序会自动创建）
mkdir -p csdn-vip/csdn-vip-bankend/data
```

### 3. 项目部署

#### 克隆项目
```bash
git clone <your-repository-url>
cd csdn-vip
```

#### 后端部署
```bash
cd csdn-vip-bankend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置数据库连接（SQLite无需额外配置）
cp config/dev.toml config/prod.toml
# 编辑 config/prod.toml，更新应用配置
```

修改 `config/prod.toml`:
```toml
[development]
APP.DEBUG = false
APP.TITLE = "CSDN文章提取系统"
APP.DESCRIPTION = "生产环境"

# SQLite数据库配置
[SQLITE]
DATABASE = "data/csdn_vip.db"

[JWT]
SECRET_KEY = "your_very_secure_secret_key_here"
ACCESS_TOKEN_EXPIRE_MINUTES = 10080
```

```bash
# 初始化系统
python init_system.py

# 启动后端（生产环境）
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:11211
```

#### 前端部署
```bash
cd csdn-vip-frontend

# 安装依赖
npm install

# 构建生产版本
npm run build

# 使用nginx或其他web服务器提供静态文件服务
```

### 4. Nginx配置

创建 `/etc/nginx/sites-available/csdn-vip`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/csdn-vip/csdn-vip-frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:11211;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/csdn-vip /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 进阶配置

### 1. 使用Systemd服务

创建 `/etc/systemd/system/csdn-vip-backend.service`:
```ini
[Unit]
Description=CSDN VIP Backend Service
After=network.target

[Service]
Type=exec
User=your-user
Group=your-group
WorkingDirectory=/path/to/csdn-vip/csdn-vip-bankend
Environment=PATH=/path/to/csdn-vip/csdn-vip-bankend/venv/bin
ExecStart=/path/to/csdn-vip/csdn-vip-bankend/venv/bin/gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:11211
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable csdn-vip-backend
sudo systemctl start csdn-vip-backend
sudo systemctl status csdn-vip-backend
```

### 2. SSL/HTTPS配置

使用Let's Encrypt获取免费SSL证书：
```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com
```

### 3. 日志配置

#### Nginx日志
```nginx
# 在server块中添加
access_log /var/log/nginx/csdn-vip.access.log;
error_log /var/log/nginx/csdn-vip.error.log;
```

#### 应用日志
确保日志目录存在：
```bash
mkdir -p /path/to/csdn-vip/csdn-vip-bankend/log
chown your-user:your-group /path/to/csdn-vip/csdn-vip-bankend/log
```

### 4. 备份策略

#### 数据库备份脚本
创建 `/etc/cron.daily/csdn-vip-backup`:
```bash
#!/bin/bash
BACKUP_DIR="/backup/csdn-vip"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# SQLite数据库备份
cp data/csdn_vip.db $BACKUP_DIR/csdn_vip_$DATE.db

# 保留30天的备份
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
```

设置权限：
```bash
sudo chmod +x /etc/cron.daily/csdn-vip-backup
```

## 🔍 监控和维护

### 1. 系统监控

#### 检查服务状态
```bash
# 检查后端服务
sudo systemctl status csdn-vip-backend

# SQLite数据库无需服务检查，文件存在即可用

# 检查Nginx
sudo systemctl status nginx

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

#### 日志监控
```bash
# 查看后端日志
tail -f /path/to/csdn-vip/csdn-vip-bankend/log/app.log

# 查看Nginx访问日志
tail -f /var/log/nginx/csdn-vip.access.log

# 查看系统日志
journalctl -u csdn-vip-backend -f
```

### 2. 性能优化

#### 数据库优化
```sql
-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_extraction_record_url_hash ON extraction_record(url_hash);
CREATE INDEX IF NOT EXISTS idx_extraction_record_token_time ON extraction_record(access_token_id, created_at);
CREATE INDEX IF NOT EXISTS idx_token_usage_log_token_time ON token_usage_log(access_token_id, request_time);

-- 分析表以更新统计信息
ANALYZE;
```

#### 应用优化
```bash
# 增加worker数量（根据CPU核心数调整）
gunicorn main:app -w 8 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:11211

# 配置连接池
# 在config/prod.toml中增加数据库连接池配置
```

### 3. 安全加固

#### 防火墙配置
```bash
# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许SSH（注意修改端口）
sudo ufw allow 22

# 启用防火墙
sudo ufw enable
```

#### 定期更新
```bash
# 创建更新脚本
cat > /home/<USER>/update-csdn-vip.sh << 'EOF'
#!/bin/bash
cd /path/to/csdn-vip
git pull
cd csdn-vip-bankend
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart csdn-vip-backend
cd ../csdn-vip-frontend
npm install
npm run build
EOF

chmod +x /home/<USER>/update-csdn-vip.sh
```

## 🚨 故障排除

### 常见问题

#### 1. 后端服务无法启动
```bash
# 检查日志
journalctl -u csdn-vip-backend -n 50

# 检查端口占用
sudo netstat -tulpn | grep 11211

# 检查数据库连接
sudo -u postgres psql -c "\l"
```

#### 2. 前端访问404
```bash
# 检查Nginx配置
sudo nginx -t

# 检查文件权限
ls -la /path/to/csdn-vip/csdn-vip-frontend/dist/

# 重启Nginx
sudo systemctl restart nginx
```

#### 3. 数据库连接失败
```bash
# 检查SQLite数据库文件是否存在
ls -la data/csdn_vip.db

# 检查文件权限
ls -la data/

# 测试SQLite连接
sqlite3 data/csdn_vip.db "SELECT 1;"
```

### 紧急恢复

#### 数据库恢复
```bash
# 从备份恢复SQLite数据库
# 停止应用服务
sudo systemctl stop csdn-vip-backend

# 恢复数据库文件
cp /backup/csdn-vip/csdn_vip_YYYYMMDD_HHMMSS.db data/csdn_vip.db

# 重启应用服务
sudo systemctl start csdn-vip-backend
```

#### 回滚代码
```bash
cd /path/to/csdn-vip
git log --oneline -10  # 查看最近的提交
git reset --hard <commit-hash>  # 回滚到指定版本
sudo systemctl restart csdn-vip-backend
```

## 📞 技术支持

如遇到部署问题，请检查以下内容：

1. 系统日志：`journalctl -xe`
2. 应用日志：检查 `log/` 目录
3. 网络连接：`telnet localhost 11211`
4. 资源使用：`htop` 或 `top`

更多技术支持，请联系系统维护人员或查看项目文档。
