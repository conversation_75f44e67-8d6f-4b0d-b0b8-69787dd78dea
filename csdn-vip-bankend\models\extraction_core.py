from .base import BaseModel, fields
from enum import IntEnum


class CoreStatus(IntEnum):
    """核心服务状态"""
    ACTIVE = 1      # 激活状态
    INACTIVE = 2    # 非激活状态
    MAINTENANCE = 3 # 维护中
    ERROR = 4       # 错误状态


class ExtractionCore(BaseModel):
    """提取核心配置表"""
    # 基础信息
    name = fields.CharField(max_length=128, null=False, description="核心服务名称")
    description = fields.TextField(null=True, description="服务描述")
    
    # API配置
    api_endpoint = fields.CharField(max_length=512, null=False, description="API端点URL")
    auth_token = fields.CharField(max_length=256, null=False, description="认证令牌")
    
    # 状态信息
    status: CoreStatus = fields.IntEnumField(CoreStatus, default=CoreStatus.ACTIVE, description="服务状态")
    
    # 性能配置
    timeout_seconds = fields.IntField(default=30, description="请求超时时间（秒）")
    max_retries = fields.IntField(default=3, description="最大重试次数")
    priority = fields.IntField(default=1, description="优先级（数字越小优先级越高）")
    
    # 负载均衡配置
    weight = fields.IntField(default=1, description="负载均衡权重")
    max_concurrent_requests = fields.IntField(default=10, description="最大并发请求数")
    current_requests = fields.IntField(default=0, description="当前请求数")
    
    # 统计信息
    total_requests = fields.BigIntField(default=0, description="总请求次数")
    success_requests = fields.BigIntField(default=0, description="成功请求次数")
    failed_requests = fields.BigIntField(default=0, description="失败请求次数")
    average_response_time = fields.FloatField(default=0.0, description="平均响应时间（秒）")
    
    # 健康检查
    last_health_check = fields.DatetimeField(null=True, description="最后健康检查时间")
    health_check_url = fields.CharField(max_length=512, null=True, description="健康检查URL")
    is_healthy = fields.BooleanField(default=True, description="是否健康")
    
    # 创建和更新信息
    created_by = fields.ForeignKeyField('template.AdminUser', related_name='created_cores', null=True, description="创建者")
    updated_by = fields.ForeignKeyField('template.AdminUser', related_name='updated_cores', null=True, description="最后更新者")

    class Meta:
        table = "extraction_core"
        indexes = [
            ("status", "priority"),
            ("is_healthy", "status"),
        ]
        
    class PydanticMeta:
        exclude = ['is_deleted']

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.success_requests / self.total_requests) * 100

    @property
    def is_available(self) -> bool:
        """是否可用"""
        return (
            self.status == CoreStatus.ACTIVE and
            self.is_healthy and
            self.current_requests < self.max_concurrent_requests
        )

    async def increment_request(self):
        """增加请求计数"""
        self.current_requests += 1
        self.total_requests += 1
        await self.save(update_fields=['current_requests', 'total_requests'])

    async def decrement_request(self):
        """减少请求计数"""
        if self.current_requests > 0:
            self.current_requests -= 1
            await self.save(update_fields=['current_requests'])

    async def record_success(self, response_time: float):
        """记录成功请求"""
        self.success_requests += 1
        # 更新平均响应时间
        if self.average_response_time == 0:
            self.average_response_time = response_time
        else:
            total_time = self.average_response_time * (self.success_requests - 1) + response_time
            self.average_response_time = total_time / self.success_requests
        await self.decrement_request()
        await self.save(update_fields=['success_requests', 'average_response_time'])

    async def record_failure(self):
        """记录失败请求"""
        self.failed_requests += 1
        await self.decrement_request()
        await self.save(update_fields=['failed_requests'])

    @classmethod
    async def get_available_cores(cls):
        """获取可用的核心服务"""
        return await cls.filter(
            status=CoreStatus.ACTIVE,
            is_healthy=True
        ).order_by('priority', 'current_requests')

    @classmethod
    async def get_best_core(cls):
        """获取最佳核心服务（负载均衡）"""
        cores = await cls.get_available_cores()
        if not cores:
            return None
        
        # 简单的权重轮询算法
        best_core = None
        best_score = float('inf')
        
        for core in cores:
            if core.current_requests >= core.max_concurrent_requests:
                continue
                
            # 计算负载分数（请求数/权重）
            score = core.current_requests / max(core.weight, 1)
            if score < best_score:
                best_score = score
                best_core = core
                
        return best_core
