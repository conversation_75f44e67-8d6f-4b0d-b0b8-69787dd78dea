from models import AdminUser
from schemas import Token, AdminUserOut
from datetime import timed<PERSON><PERSON>, datetime
from pytz import timezone
from fastapi import APIRouter, Depends
from fastapi.security import OAuth2PasswordRequestForm
from core.auth.base import verify_password, create_access_token, get_current_active_user
from config import settings
from core.response import ResultResponse
from core.exce.base import UserError

login_router = APIRouter(
    prefix="/login",
    tags=["登录"],
)


@login_router.post(
    "",
    name="login",
    summary="login",
    response_model=ResultResponse[Token]
)
async def login(
        form_data: OAuth2PasswordRequestForm = Depends()
) -> ResultResponse[Token]:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    token = await login_access_token(form_data)
    return ResultResponse[Token](
        result=token
    )


@login_router.post(
    "-openapi",
    name="openapi login",
    summary="openapi login",
    response_model=Token
)
async def login_access_token(
        form_data: OAuth2PasswordRequestForm = Depends()
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = await AdminUser.get_or_none(username=form_data.username)
    if not user or user.is_deleted:
        raise UserError('账号或密码错误')
    elif not user.is_active:
        raise UserError('用户未激活')
    if not verify_password(form_data.password, user.hashed_password):
        raise UserError('账号或密码错误')
    user.last_login_time = datetime.now().astimezone(timezone('UTC'))
    await user.save()
    access_token_expires = timedelta(minutes=settings.JWT.ACCESS_TOKEN_EXPIRE_MINUTES)
    return Token(
        access_token=create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        token_type="bearer"
    )


@login_router.get(
    "-test-token",
    name="token test",
    summary="token test",
    response_model=ResultResponse[AdminUserOut]
)
async def test_token(current_user: AdminUser = Depends(get_current_active_user)) -> ResultResponse[AdminUserOut]:
    """
    Test access token
    """
    current_user = AdminUserOut.from_orm(current_user)
    return ResultResponse[AdminUserOut](result=current_user)
