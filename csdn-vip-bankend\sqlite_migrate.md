# SQLite3 迁移说明

本系统已从PostgreSQL迁移到SQLite3，以简化部署和使用。

## 🔄 主要变更

### 1. 依赖变更
- ❌ 移除: `asyncpg`, `psycopg2`
- ✅ 添加: `aiosqlite`

### 2. 配置变更
- **原配置** (PostgreSQL):
  ```toml
  [POSTGRESQL]
  USER = "postgres"
  PASSWORD = "123456"
  HOST = "localhost"
  PORT = 5432
  DATABASE = "product-template"
  ```

- **新配置** (SQLite):
  ```toml
  [SQLITE]
  DATABASE = "data/csdn_vip.db"
  ```

### 3. 连接字符串变更
- **原连接**: `postgres://user:pass@host:port/db`
- **新连接**: `sqlite://data/csdn_vip.db`

## 📁 文件结构
```
csdn-vip-bankend/
├── data/
│   └── csdn_vip.db          # SQLite数据库文件
├── config/
│   ├── dev.toml            # 开发环境配置
│   └── prod.toml           # 生产环境配置
└── ...
```

## 🚀 使用优势

### SQLite3 优势:
1. **零配置**: 无需安装和配置数据库服务器
2. **轻量级**: 单文件数据库，易于备份和迁移
3. **高性能**: 对于中小型应用性能优异
4. **原子性**: 完整的ACID事务支持
5. **跨平台**: 在所有平台上都能正常工作

### 异步支持:
- 使用 `aiosqlite` 包提供完整的异步支持
- 保持原有的 Tortoise ORM 异步特性
- 所有API接口保持异步处理

## 🔧 运维说明

### 备份
```bash
# 简单文件复制即可
cp data/csdn_vip.db backup/csdn_vip_$(date +%Y%m%d_%H%M%S).db
```

### 恢复
```bash
# 停止服务
sudo systemctl stop csdn-vip-backend

# 恢复数据库
cp backup/csdn_vip_YYYYMMDD_HHMMSS.db data/csdn_vip.db

# 启动服务
sudo systemctl start csdn-vip-backend
```

### 监控
```bash
# 检查数据库文件
ls -lh data/csdn_vip.db

# 查看数据库信息
sqlite3 data/csdn_vip.db ".schema"
sqlite3 data/csdn_vip.db ".tables"
```

## ⚠️ 注意事项

1. **并发限制**: SQLite在高并发写入时可能成为瓶颈
2. **网络访问**: SQLite不支持网络访问，仅限本地
3. **权限管理**: 确保应用有数据目录的读写权限
4. **备份策略**: 建议定期备份数据库文件

## 🔄 如需回滚到PostgreSQL

如果需要切换回PostgreSQL，请：

1. 恢复依赖:
   ```bash
   pip install asyncpg psycopg2
   pip uninstall aiosqlite
   ```

2. 恢复配置文件中的数据库设置

3. 更新连接字符串格式

4. 重新初始化数据库
