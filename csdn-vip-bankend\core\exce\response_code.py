class HttpStatus:
    # 请求正常
    HTTP_200_OK = 200

    # 没有访问权限
    HTTP_403_FORBIDDEN = 403

    # 目标不存在
    HTTP_404 = 404

    # 密码验证错误
    HTTP_410_PASSWORD_VERIFY_ERROR = 410

    # 用户不存在
    HTTP_419_USER_NOT_FOUND = 419

    # Token 过期
    HTTP_420_TOKEN_EXPIRED = 420

    # 内部参数校验失败
    HTTP_421_INNER_PARAM_EXCEPT = 421

    # 请求参数格式错误
    HTTP_422_QUERY_PARAM_EXCEPT = 422

    # 无手机号访问，需要验证
    HTTP_426_PHONE_EXCEPT = 426

    # 正常登录无手机号，需要验证
    HTTP_427_NO_PHONE = 427

    # 未设置地区
    HTTP_428_NO_AREA = 428

    # 未开通VIP
    HTTP_429_NO_VIP = 429

    # 短信发送失败
    HTTP_450_SMS_FAIL = 450

    # 抖音url错误
    HTTP_460_DOUYIN_URL_EXCEPT = 460

    # 抖音url错误
    HTTP_461_WECHAT_URL_EXCEPT = 461

    # 高德url错误
    HTTP_462_GAODE_URL_EXCEPT = 462

    # 验证码验证失败
    HTTP_451_VERIFY_CODE_FAIL = 451

    # 服务端错误
    HTTP_500_INTERNAL_SERVER_ERROR = 500

    # 数据库错误
    HTTP_501_DATABASE_OPERATIONAL_ERROR = 501

    # 数据库插入错误
    HTTP_502_DATABASE_DUPLICATE_ERROR = 502

    # 角色不存在
    HTTP_600_ROLE_NOT_EXIST = 600

    # 角色存在
    HTTP_601_ROLE_EXIST = 601

    # 域不存在
    HTTP_700_DOMAIN_NOT_EXIST = 700

    # 域存在
    HTTP_701_DOMAIN_EXIST = 701

    # 积分不足
    HTTP_850_COUPON_ERROR = 850

    # 库存不足
    HTTP_900_NOT_ENOUGH_STOCK = 900

    # 第三方下单失败
    HTTP_901_THIRD_FAILED = 901

