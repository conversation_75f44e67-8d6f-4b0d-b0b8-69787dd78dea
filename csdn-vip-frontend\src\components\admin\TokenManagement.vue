<template>
  <div class="token-management">
    <div class="management-header">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建Token
      </el-button>
      <el-button @click="refreshTokens">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- Token列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Key /></el-icon>
          <span>访问令牌列表</span>
          <div class="header-info">
            <span>共 {{ tokenList.length }} 个令牌</span>
          </div>
        </div>
      </template>

      <el-table :data="tokenList" style="width: 100%" v-loading="isLoading">
        <el-table-column prop="token" label="Token" show-overflow-tooltip>
          <template #default="scope">
            <div class="token-cell">
              <span class="token-value">{{ maskToken(scope.row.token) }}</span>
              <el-button text type="primary" size="small" @click="copyToken(scope.row.token)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'time' ? 'success' : 'warning'">
              {{ scope.row.type === 'time' ? '时间限制' : '次数限制' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="remaining_count" label="剩余次数" width="120">
          <template #default="scope">
            {{ scope.row.remaining_count !== null ? scope.row.remaining_count : '不限制' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="expiry_time" label="过期时间" width="180">
          <template #default="scope">
            {{ scope.row.expiry_time ? formatTime(scope.row.expiry_time) : '永久有效' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'">
              {{ scope.row.status ? '有效' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-space>
              <el-button text type="primary" @click="generateLink(scope.row)">
                <el-icon><Link /></el-icon>
                生成链接
              </el-button>
              <el-button 
                text 
                :type="scope.row.status ? 'warning' : 'success'"
                @click="toggleTokenStatus(scope.row)"
              >
                {{ scope.row.status ? '禁用' : '启用' }}
              </el-button>
              <el-button text type="danger" @click="deleteToken(scope.row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建Token对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建访问令牌" width="500px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="令牌类型" prop="type">
          <el-radio-group v-model="createForm.type">
            <el-radio value="time">时间限制</el-radio>
            <el-radio value="count">次数限制</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          v-if="createForm.type === 'time'" 
          label="有效期" 
          prop="expiry_days"
        >
          <el-input-number 
            v-model="createForm.expiry_days" 
            :min="1" 
            :max="365"
            placeholder="天数"
          />
          <span class="form-tip">天</span>
        </el-form-item>
        
        <el-form-item 
          v-if="createForm.type === 'count'" 
          label="使用次数" 
          prop="max_count"
        >
          <el-input-number 
            v-model="createForm.max_count" 
            :min="1" 
            :max="10000"
            placeholder="次数"
          />
          <span class="form-tip">次</span>
        </el-form-item>
        
        <el-form-item label="备注" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="可选，用于标识该令牌的用途"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="createToken" :loading="isCreating">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成链接对话框 -->
    <el-dialog v-model="showLinkDialog" title="访问链接" width="600px">
      <div class="link-content">
        <p class="link-description">用户可以通过以下链接直接访问提取页面：</p>
        <el-input 
          v-model="generatedLink" 
          readonly 
          class="link-input"
        >
          <template #append>
            <el-button @click="copyLink">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </template>
        </el-input>
        
        <div class="qr-section">
          <p>二维码：</p>
          <div class="qr-code" ref="qrCodeRef"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Plus, Refresh, Key, CopyDocument, Link, Delete 
} from '@element-plus/icons-vue'

// 模拟数据
const tokenList = ref([
  {
    id: 1,
    token: 'tk_abc123def456ghi789jkl012mno345pqr678',
    type: 'time',
    remaining_count: null,
    expiry_time: '2024-12-31T23:59:59',
    status: true,
    created_at: '2024-01-15T10:30:00',
    description: '测试用户令牌'
  },
  {
    id: 2,
    token: 'tk_xyz789uvw456rst123opq890lmn567hij234',
    type: 'count',
    remaining_count: 50,
    expiry_time: null,
    status: true,
    created_at: '2024-01-10T14:20:00',
    description: 'VIP用户令牌'
  }
])

const isLoading = ref(false)
const isCreating = ref(false)
const showCreateDialog = ref(false)
const showLinkDialog = ref(false)
const generatedLink = ref('')
const qrCodeRef = ref()

// 创建表单
const createForm = reactive({
  type: 'time',
  expiry_days: 30,
  max_count: 100,
  description: ''
})

const createFormRef = ref<FormInstance>()

const createRules: FormRules = {
  type: [
    { required: true, message: '请选择令牌类型', trigger: 'change' }
  ],
  expiry_days: [
    { required: true, message: '请输入有效天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '有效期应在1-365天之间', trigger: 'blur' }
  ],
  max_count: [
    { required: true, message: '请输入使用次数', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '使用次数应在1-10000次之间', trigger: 'blur' }
  ]
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 隐藏token
const maskToken = (token: string) => {
  if (token.length <= 8) return token
  return token.substring(0, 8) + '...' + token.substring(token.length - 8)
}

// 复制token
const copyToken = async (token: string) => {
  try {
    await navigator.clipboard.writeText(token)
    ElMessage.success('Token已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 刷新token列表
const refreshTokens = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('Refresh tokens failed:', error)
    ElMessage.error('刷新失败')
  } finally {
    isLoading.value = false
  }
}

// 创建token
const createToken = async () => {
  if (!createFormRef.value) return
  
  try {
    const valid = await createFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isCreating.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成新token（模拟）
    const newToken = {
      id: Date.now(),
      token: `tk_${Math.random().toString(36).substr(2, 32)}`,
      type: createForm.type,
      remaining_count: createForm.type === 'count' ? createForm.max_count : null,
      expiry_time: createForm.type === 'time' 
        ? new Date(Date.now() + createForm.expiry_days * 24 * 60 * 60 * 1000).toISOString()
        : null,
      status: true,
      created_at: new Date().toISOString(),
      description: createForm.description
    }
    
    tokenList.value.unshift(newToken)
    showCreateDialog.value = false
    ElMessage.success('Token创建成功')
    
    // 重置表单
    createFormRef.value?.resetFields()
  } catch (error) {
    console.error('Create token failed:', error)
    ElMessage.error('创建失败')
  } finally {
    isCreating.value = false
  }
}

// 切换token状态
const toggleTokenStatus = async (token: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要${token.status ? '禁用' : '启用'}这个令牌吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    token.status = !token.status
    ElMessage.success(`令牌已${token.status ? '启用' : '禁用'}`)
  } catch {
    // 用户取消
  }
}

// 删除token
const deleteToken = async (token: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个令牌吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    const index = tokenList.value.findIndex(t => t.id === token.id)
    if (index > -1) {
      tokenList.value.splice(index, 1)
      ElMessage.success('令牌已删除')
    }
  } catch {
    // 用户取消
  }
}

// 生成访问链接
const generateLink = async (token: any) => {
  const baseUrl = window.location.origin
  generatedLink.value = `${baseUrl}/?token=${token.token}`
  showLinkDialog.value = true
  
  // 生成二维码（这里需要引入二维码库，暂时省略）
  await nextTick()
  if (qrCodeRef.value) {
    qrCodeRef.value.innerHTML = '<div style="text-align: center; color: #999;">二维码功能需要引入QR库</div>'
  }
}

// 复制链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(generatedLink.value)
    ElMessage.success('链接已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}
</script>

<style scoped>
.token-management {
  height: 100%;
}

.management-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.header-info {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.token-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.token-value {
  font-family: 'Courier New', monospace;
  color: #606266;
}

.form-tip {
  margin-left: 0.5rem;
  color: #909399;
  font-size: 14px;
}

.link-content {
  text-align: center;
}

.link-description {
  margin-bottom: 1rem;
  color: #606266;
}

.link-input {
  margin-bottom: 2rem;
}

.qr-section {
  margin-top: 2rem;
}

.qr-section p {
  margin-bottom: 1rem;
  color: #606266;
}

.qr-code {
  display: flex;
  justify-content: center;
  min-height: 150px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  align-items: center;
  background-color: #fafafa;
}
</style>
