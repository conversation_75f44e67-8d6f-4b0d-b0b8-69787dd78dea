import base64
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class AES128CBCCrypto(object):
    def __init__(self, key, iv):
        self.key = key
        self.mode = AES.MODE_CBC
        self.iv = iv

    @staticmethod
    def md5_sum(tmp):
        m = hashlib.md5()
        m.update(tmp.encode("utf8"))
        return m.hexdigest()

    def set_key_and_iv(self):
        tmp = self.md5_sum(self.key)
        self.iv = tmp[0:16].encode("utf8")
        self.key = self.key.encode("utf8")

    def encrypt(self, text):
        data = pad(text.encode('utf8'), 16)
        cipher = AES.new(self.key, self.mode, self.iv)
        encrypted_bytes = cipher.encrypt(data)
        encode_strs = base64.b64encode(encrypted_bytes)
        enc_text = encode_strs.decode('utf8')
        return enc_text

    def decrypt(self, text):
        cryptor = AES.new(self.key, self.mode, self.iv)
        encode_bytes = base64.decodebytes(text)
        # 需要注意进行base64解码字符串
        plain_text = cryptor.decrypt(encode_bytes)
        plain_text = unpad(plain_text, 16)
        return plain_text.decode("utf8")
