<template>
  <div class="user-management">
    <div class="management-header">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建用户
      </el-button>
      <el-button @click="refreshUsers">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户名或邮箱"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 用户列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>用户列表</span>
          <div class="header-info">
            <span>共 {{ filteredUserList.length }} 个用户</span>
          </div>
        </div>
      </template>

      <el-table :data="paginatedUsers" style="width: 100%" v-loading="isLoading">
        <el-table-column prop="username" label="用户名" width="150">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :size="30" :src="scope.row.avatar">
                {{ scope.row.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="email" label="邮箱" show-overflow-tooltip />
        
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">
              {{ getRoleText(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="scope">
            <span v-if="scope.row.last_login">
              {{ formatTime(scope.row.last_login) }}
            </span>
            <span v-else class="text-muted">从未登录</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-space>
              <el-button text type="primary" @click="viewUserDetail(scope.row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button text type="info" @click="editUser(scope.row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                text 
                :type="scope.row.status ? 'warning' : 'success'"
                @click="toggleUserStatus(scope.row)"
              >
                {{ scope.row.status ? '禁用' : '启用' }}
              </el-button>
              <el-button 
                text 
                type="danger" 
                @click="deleteUser(scope.row)"
                :disabled="scope.row.role === 'super_admin'"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredUserList.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="isEditing ? '编辑用户' : '创建用户'" 
      width="500px"
    >
      <el-form :model="userForm" :rules="userRules" ref="userFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            placeholder="请输入用户名"
            :disabled="isEditing"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            type="password" 
            show-password
            :placeholder="isEditing ? '留空则不修改密码' : '请输入密码'"
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="运营人员" value="operator" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch v-model="userForm.status" />
        </el-form-item>
        
        <el-form-item label="备注" prop="description">
          <el-input 
            v-model="userForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="可选，用于描述用户信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="isSaving">
            {{ isEditing ? '保存' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="用户详情" width="600px">
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">
            {{ selectedUser.username }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ selectedUser.email }}
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleType(selectedUser.role)">
              {{ getRoleText(selectedUser.role) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedUser.status ? 'success' : 'danger'">
              {{ selectedUser.status ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedUser.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">
            <span v-if="selectedUser.last_login">
              {{ formatTime(selectedUser.last_login) }}
            </span>
            <span v-else class="text-muted">从未登录</span>
          </el-descriptions-item>
          <el-descriptions-item label="登录次数">
            {{ selectedUser.login_count }}
          </el-descriptions-item>
          <el-descriptions-item label="最后IP">
            {{ selectedUser.last_ip || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ selectedUser.description || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 用户统计 -->
        <div class="user-stats">
          <h4>使用统计</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="Token数量" :value="selectedUser.token_count" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="提取次数" :value="selectedUser.extraction_count" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="成功率" :value="selectedUser.success_rate" suffix="%" />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  Plus, Refresh, Search, User, View, Edit, Delete
} from '@element-plus/icons-vue'

// 模拟数据
const userList = ref([
  {
    id: 1,
    username: 'superadmin',
    email: '<EMAIL>',
    role: 'super_admin',
    status: true,
    last_login: '2024-01-15T10:30:00',
    created_at: '2024-01-01T00:00:00',
    login_count: 150,
    last_ip: '*************',
    description: '系统超级管理员',
    avatar: '',
    token_count: 25,
    extraction_count: 1500,
    success_rate: 95.5
  },
  {
    id: 2,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    status: true,
    last_login: '2024-01-15T09:45:00',
    created_at: '2024-01-02T00:00:00',
    login_count: 89,
    last_ip: '*************',
    description: '系统管理员',
    avatar: '',
    token_count: 12,
    extraction_count: 800,
    success_rate: 92.3
  },
  {
    id: 3,
    username: 'operator01',
    email: '<EMAIL>',
    role: 'operator',
    status: true,
    last_login: '2024-01-14T16:20:00',
    created_at: '2024-01-05T00:00:00',
    login_count: 45,
    last_ip: '*************',
    description: '运营人员',
    avatar: '',
    token_count: 8,
    extraction_count: 350,
    success_rate: 88.7
  },
  {
    id: 4,
    username: 'user001',
    email: '<EMAIL>',
    role: 'user',
    status: false,
    last_login: null,
    created_at: '2024-01-10T00:00:00',
    login_count: 0,
    last_ip: null,
    description: '普通用户（已禁用）',
    avatar: '',
    token_count: 0,
    extraction_count: 0,
    success_rate: 0
  }
])

const isLoading = ref(false)
const isSaving = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const isEditing = ref(false)
const editingId = ref<number | null>(null)
const searchKeyword = ref('')
const selectedUser = ref<any>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  role: 'user',
  status: true,
  description: ''
})

const userFormRef = ref<FormInstance>()

const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { 
      validator: (rule: any, value: string, callback: any) => {
        if (!isEditing.value && !value) {
          callback(new Error('请输入密码'))
        } else if (value && value.length < 6) {
          callback(new Error('密码长度不能少于6位'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 角色映射
const roleMap = {
  super_admin: { text: '超级管理员', type: 'danger' },
  admin: { text: '管理员', type: 'warning' },
  operator: { text: '运营人员', type: 'primary' },
  user: { text: '普通用户', type: 'info' }
}

// 过滤用户列表
const filteredUserList = computed(() => {
  if (!searchKeyword.value) return userList.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return userList.value.filter(user => 
    user.username.toLowerCase().includes(keyword) ||
    user.email.toLowerCase().includes(keyword)
  )
})

// 分页用户列表
const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUserList.value.slice(start, end)
})

// 获取角色文本
const getRoleText = (role: string) => {
  return roleMap[role as keyof typeof roleMap]?.text || '未知'
}

// 获取角色类型
const getRoleType = (role: string) => {
  return roleMap[role as keyof typeof roleMap]?.type || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 刷新用户列表
const refreshUsers = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('Refresh users failed:', error)
    ElMessage.error('刷新失败')
  } finally {
    isLoading.value = false
  }
}

// 查看用户详情
const viewUserDetail = (user: any) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

// 编辑用户
const editUser = (user: any) => {
  isEditing.value = true
  editingId.value = user.id
  
  userForm.username = user.username
  userForm.email = user.email
  userForm.password = ''
  userForm.role = user.role
  userForm.status = user.status
  userForm.description = user.description
  
  showCreateDialog.value = true
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return
  
  try {
    const valid = await userFormRef.value.validate()
    if (!valid) return
  } catch {
    return
  }

  isSaving.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (isEditing.value && editingId.value) {
      // 编辑模式
      const index = userList.value.findIndex(u => u.id === editingId.value)
      if (index > -1) {
        Object.assign(userList.value[index], {
          email: userForm.email,
          role: userForm.role,
          status: userForm.status,
          description: userForm.description
        })
      }
      ElMessage.success('用户更新成功')
    } else {
      // 新建模式
      const newUser = {
        id: Date.now(),
        username: userForm.username,
        email: userForm.email,
        role: userForm.role,
        status: userForm.status,
        last_login: null,
        created_at: new Date().toISOString(),
        login_count: 0,
        last_ip: null,
        description: userForm.description,
        avatar: '',
        token_count: 0,
        extraction_count: 0,
        success_rate: 0
      }
      
      userList.value.unshift(newUser)
      ElMessage.success('用户创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    console.error('Save user failed:', error)
    ElMessage.error('保存失败')
  } finally {
    isSaving.value = false
  }
}

// 切换用户状态
const toggleUserStatus = async (user: any) => {
  if (user.role === 'super_admin') {
    ElMessage.warning('不能禁用超级管理员')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要${user.status ? '禁用' : '启用'}用户 "${user.username}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    user.status = !user.status
    ElMessage.success(`用户已${user.status ? '启用' : '禁用'}`)
  } catch {
    // 用户取消
  }
}

// 删除用户
const deleteUser = async (user: any) => {
  if (user.role === 'super_admin') {
    ElMessage.warning('不能删除超级管理员')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    const index = userList.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      userList.value.splice(index, 1)
      ElMessage.success('用户已删除')
    }
  } catch {
    // 用户取消
  }
}

// 重置表单
const resetForm = () => {
  isEditing.value = false
  editingId.value = null
  
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.role = 'user'
  userForm.status = true
  userForm.description = ''
  
  userFormRef.value?.resetFields()
}
</script>

<style scoped>
.user-management {
  height: 100%;
}

.management-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.search-box {
  margin-left: auto;
  width: 300px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.header-info {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.username {
  font-weight: 500;
}

.text-muted {
  color: #c0c4cc;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.user-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.user-stats {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e4e7ed;
}

.user-stats h4 {
  margin-bottom: 1rem;
  color: #303133;
}

@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    margin-left: 0;
    width: 100%;
  }
}
</style>
