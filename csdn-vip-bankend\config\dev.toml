[development]
APP.DEBUG = true
APP.TITLE = "product-template-backend"
APP.DESCRIPTION = "template"
APP.DOCS_URL = "/openapi/docs" # 文档地址 默认为docs
APP.OPENAPI_URL = "/openapi/openapi.json" # 文档关联请求数据接口
APP.REDOC_URL = "/openapi/redoc" # redoc 文档
APP.API_VERSION = "/api/v1" # API版本
APP.LOG_PATH = "./log" # 日志路径

# SQLite数据库配置
SQLITE.DATABASE = "data/csdn_vip.db"  # 数据库文件路径

# JWT鉴权
JWT.ALGORITHM = "HS256"
JWT.SECRET_KEY = "aeq)s(*&(&)()WEQasd8**&^9asda_asdasd*&*&^+_sda11"
JWT.ACCESS_TOKEN_EXPIRE_MINUTES = 10080 # token过期时间 分钟

# 腾讯COS
TENCENT.COS.SECRET_ID = "AKIDjIfpY3PYUDi04wc4KMAk8NOAiZLuGQM5"
TENCENT.COS.SECRET_KEY = "aw75rPksoX2KVUFoSvAJH3bu0ua2GI5e"
TENCENT.COS.BUCKET = "dps-1314564919"
TENCENT.COS.REGION = "ap-shanghai"

# CASBIN
CASBIN.CASBIN_MODEL_PATH = "config/rbac.conf"

# 时区
TIMEZONE = "Asia/Shanghai"
